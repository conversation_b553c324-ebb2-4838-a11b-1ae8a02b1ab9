{"kind": "collectionType", "collectionName": "class_schedules", "info": {"singularName": "class-schedule", "pluralName": "class-schedules", "displayName": "Class_schedule", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "grade": {"type": "enumeration", "enum": ["Lớp 10", "Lớp 11", "Lớp 12"]}, "date": {"type": "date"}, "schedule": {"type": "json"}, "offline_forms": {"type": "relation", "relation": "manyToMany", "target": "api::offline-form.offline-form", "inversedBy": "class_schedules"}, "courses": {"type": "relation", "relation": "manyToMany", "target": "api::course.course", "inversedBy": "class_schedules"}}}