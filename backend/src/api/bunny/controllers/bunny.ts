import {env} from "@strapi/utils";
import axios from "axios";
import jwt from "jsonwebtoken";
import crypto from 'crypto'

const BUNNY_API_KEY = process.env.BUNNY_API_KEY;
const BUNNY_LIBRARY_ID = process.env.BUNNY_LIBRARY_ID;
const BUNNY_BASE_URL = process.env.BUNNY_BASE_URL;
const BUNNY_EMBED_KEY = process.env.BUNNY_EMBED_KEY;
const DOMAIN = process.env.DOMAIN;
export default {
    async getSrcVideoPlay(ctx) {
        try {
            let body = ctx.request.body as any;
            const expires = Math.floor(Date.now() / 1000) + 3600 / 30;
            const raw = BUNNY_EMBED_KEY + body.videoId + expires;
            const token = crypto.createHash('sha256').update(raw).digest('hex');
            const iframeUrl = `https://iframe.mediadelivery.net/embed/${BUNNY_LIBRARY_ID}/${body.videoId}?token=${token}&expires=${expires}&autoplay=true&loop=false&muted=false&preload=true&responsive=true`;


            const videos = await strapi.entityService.findMany('api::upload-video.upload-video', {
                filters: { video_id: body.videoId }
            });



            return ctx.send({
                message: '',
                success: true,
                data: {iframeUrl: iframeUrl,title: videos[0].title, description: videos[0].description, createdAt: videos[0].createdAt}
            })


        } catch (err) {
            return ctx.send({
                message: 'Có lỗi xảy ra khi lấy video: ' + err,
                success: false
            })
            ctx.status = 500;
        }
    },
    async getListVideo(ctx) {
        try {
            let body = ctx.request.body as any;
            let urlOrigin = "https://video.bunnycdn.com/library";
            let url = "https://video.bunnycdn.com/library";

            if (body) {
                url = (body.libraryId ?  url + "/" + body.libraryId :  url + `/${BUNNY_LIBRARY_ID}` ) + '/videos?';
                url = body.page ?  url + "page=" + body.page :  url + "page=1";
                url = body.itemsPerPage ?  url + "&itemsPerPage=" + body.itemsPerPage :   url + "&itemsPerPage=100";
                if (body.collection) {
                    url = url + "&collection=" + body.collection;
                }
                if (body.orderBy) {
                    url = url + "&orderBy=" + body.orderBy;
                }
                if (body.videoId) {
                    url = (body.libraryId ?  urlOrigin + "/" + body.libraryId :  urlOrigin + `/${BUNNY_LIBRARY_ID}` ) + `/videos/${body.videoId}`;
                }

                const options = {
                    method: 'GET',
                    url: url,
                    headers: {
                        accept: 'application/json',
                        AccessKey: `${BUNNY_API_KEY}`
                    }
                };
                await axios
                    .request(options)
                    .then(res => {
                        return ctx.body = {
                            success: true,
                            message: "Lấy video thành công",
                            data: res.data
                        };
                    })
                    .catch(err => console.error(err));
            }

        } catch (err) {
            ctx.send({
                message: 'Có lỗi xảy ra khi lấy video: ' + err,
                success: false
            })
            ctx.status = 500;
        }
    },
};