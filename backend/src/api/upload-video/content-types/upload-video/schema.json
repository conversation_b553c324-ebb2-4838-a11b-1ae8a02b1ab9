{"kind": "collectionType", "collectionName": "upload_videos", "info": {"singularName": "upload-video", "pluralName": "upload-videos", "displayName": "upload_video", "description": ""}, "options": {"draftAndPublish": true}, "attributes": {"title": {"type": "string"}, "video_id": {"type": "text"}, "stream_url": {"type": "string"}, "thumbnail": {"type": "string"}, "description": {"type": "text"}, "grades": {"type": "relation", "relation": "manyToMany", "target": "api::grade.grade", "inversedBy": "upload_videos"}, "chapters": {"type": "relation", "relation": "oneToMany", "target": "api::chapter.chapter"}, "status_video": {"type": "enumeration", "enum": ["Queued", "Processing", "Encoding", "Finished", "Resolution finished"]}, "collection_id": {"type": "string"}}}