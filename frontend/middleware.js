import { NextResponse } from 'next/server'

export function middleware(request) {
    debugger
    // Lấy cookie token
    const token = request.cookies.get('access_token')?.value
    const user_data = request.cookies.get('user_data')?.value

    // L<PERSON>y đường dẫn hiện tại
    const { pathname } = request.nextUrl

    // Xử lý riêng cho URL có dấu $ ở cuối
    if (pathname === '/$') {
        return NextResponse.redirect(new URL('/', request.url))
    }

    // Thêm điều kiện kiểm tra các đường dẫn API
    const isApiPath = pathname.startsWith('/api/');

    // Các trang không cần xác thực
    const publicPaths = [
        '/dang-nhap',
        '/dang-ky',
        '/quen-mat-khau',
        '/',
        '/khoa-hoc',
        '/bai-viet',
        '/hoc-tai-trung-tam',
        '/thanh-toan',
        '/hoa-don',
        '/xac-thuc',
        '/mat-khau-moi',
        '/chinh-sach-bao-mat',
        '/dieu-khoan',
        '/sitemap.xml', // Cho phép truy cập sitemap
        '/robots.txt', // Cho phép truy cập robots.txt
    ]

    // Các trang auth luôn được phép truy cập
    const authPaths = [
        '/dang-nhap',
        '/dang-ky',
        '/quen-mat-khau',
        '/xac-thuc',
        '/mat-khau-moi'
    ]

    // Kiểm tra xem có phải là trang public không 

    const isPublicPath = publicPaths.some(path =>
        pathname === path ||
        pathname.startsWith('/api/auth/') ||  // Thêm điều kiện này
        pathname.startsWith('/khoa-hoc/') ||
        pathname.startsWith('/bai-viet/')
    )

    // Kiểm tra xem có phải là trang auth không
    const isAuthPath = authPaths.some(path => pathname === path)

    // Kiểm tra xem có phải là trang quản lý không
    const isManagementPath = pathname === '/quan-ly' || pathname.startsWith('/quan-ly/')

    // Kiểm tra xem có phải là trang thông tin cá nhân không
    const isPersonalInfoPath = pathname === '/thong-tin-ca-nhan'

    // Kiểm tra xem có phải là trang tài khoản hoặc thông báo không
    const isAccountPath = pathname === '/tai-khoan' || pathname === '/thong-bao'

    // Kiểm tra xem có phải là trang static không (cho các tài nguyên tĩnh)
    const isStaticResource = /\.(jpg|jpeg|png|gif|svg|css|js|webmanifest)$/.test(pathname)

    // Nếu là API request, cho phép đi qua mà không can thiệp
    if (isApiPath) {
        return NextResponse.next();
    }

    // Nếu không có token và đang truy cập trang cần xác thực
    if (!token && !isPublicPath && !isStaticResource) {
        // Chuyển hướng về trang đăng nhập
        const url = new URL('/dang-nhap', request.url)
        url.searchParams.set('callbackUrl', pathname)
        return NextResponse.redirect(url)
    }

    // Kiểm tra thông tin cá nhân nếu có token và cookie user_data
    if (token && user_data && !isPersonalInfoPath && !isAuthPath && !isStaticResource) {
        try {
            // Parse user_data từ cookie
            const userData = JSON.parse(user_data)

            // Kiểm tra xem user đã có đầy đủ thông tin cá nhân chưa
            const hasFullPersonalInfo = userData.fullname && userData.date && (userData.gender !== undefined)

            // Nếu chưa có đủ thông tin và không đang ở trang thông tin cá nhân, chuyển hướng đến trang thông tin cá nhân
            if (!hasFullPersonalInfo) {
                return NextResponse.redirect(new URL('/thong-tin-ca-nhan', request.url))
            }
        } catch (error) {
            console.error('Error parsing user_data:', error)
        }
    }

    // Kiểm tra nếu user đã có đơn hàng completed (thông qua completedOrderInfo trong local storage)
    // Lưu ý: Middleware không thể truy cập localStorage, nhưng chúng ta có thể kiểm tra qua cookies
    const hasCompletedOrder = request.cookies.get('hasCompletedOrder')?.value === 'true'

    // Nếu user đã có đơn hàng completed và đang cố truy cập các trang không phải quản lý, auth, tài khoản hoặc thông báo
    if (token && hasCompletedOrder && !isManagementPath && !isAuthPath && !isAccountPath && !isStaticResource) {
        // Chuyển hướng về trang quản lý
        return NextResponse.redirect(new URL('/quan-ly', request.url))
    }

    // Nếu user chưa có đơn hàng completed nhưng cố gắng truy cập trang quản lý
    if (token && !hasCompletedOrder && isManagementPath) {
        // Chuyển hướng về trang chủ với thông báo
        const url = new URL('/', request.url)

        return NextResponse.redirect(url)
    }

    // Nếu đã có token và đang truy cập các trang đăng nhập/đăng ký
    if (token && (pathname === '/dang-nhap' || pathname === '/dang-ky')) {
        // Chuyển hướng về trang chủ
        return NextResponse.redirect(new URL('/', request.url))
    }

    return NextResponse.next()
}

// Chỉ áp dụng middleware với các đường dẫn sau
export const config = {
    matcher: [
        /*
         * Match all request paths except for the ones starting with:
         * - _next/static (static files)
         * - _next/image (image optimization files)
         * - favicon.ico (favicon file)
         */
        '/((?!_next/static|_next/image|favicon.ico).*)',
    ],
} 