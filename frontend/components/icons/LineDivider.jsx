import {CommonUtil} from "@/utils/CommonUtil";

const LineDivider = ({ width = null, height = 1, stroke = '#E9EAEB' }) => {
    const strokeWidth = CommonUtil.getStrokeWidth({ width: width ?? 20, height: height ?? 1 }); // Set mặc định cho getStrokeWidth

    const viewBox = width ? `0 0 ${width} ${height}` : `0 0 100 1`; // Default 100px nếu không có width

    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width={width ?? '100%'}   // Nếu không có width, dùng 100%
            height={height}
            viewBox={width ? viewBox : ''}
            fill="none"
            className={`${width ? '' : 'w-full'} h-[${height}px]`}
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M391 1H0V0H391V1Z"
                fill={stroke}
                strokeWidth={strokeWidth}
            />
        </svg>
    );
};

export default LineDivider;
