"use client";

import React, { useState, useContext } from "react";
import { UserContext } from "../../context/UserProvider";
import { ClipLoader } from "react-spinners";
import TextField from "../TextField";
import Button from "../Button";
import strapi from "../../app/api/strapi";

/**
 * Component hiển thị popup đổi mật khẩu
 * @param {Object} props
 * @param {boolean} props.isOpen - Trạng thái mở của popup
 * @param {Function} props.onClose - Hàm xử lý khi đóng popup
 * @returns {JSX.Element}
 */
const PasswordChangeModal = ({ isOpen, onClose }) => {
  const { user } = useContext(UserContext);
  const [isSaving, setIsSaving] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [formData, setFormData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  if (!isOpen) return null;

  // Xử lý khi click bên ngoài modal
  const handleOutsideClick = (e) => {
    if (e.target.id === "password-modal-backdrop") {
      onClose();
    }
  };

  // Xử lý khi thay đổi input
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Xử lý khi lưu thông tin
  const handleSaveChanges = async () => {
    setIsSaving(true);
    setErrorMessage("");
    setSuccessMessage("");

    try {
      // Kiểm tra các trường bắt buộc
      if (
        !formData.currentPassword ||
        !formData.newPassword ||
        !formData.confirmPassword
      ) {
        setErrorMessage("Vui lòng điền đầy đủ thông tin");
        setIsSaving(false);
        return;
      }

      // Kiểm tra mật khẩu mới và xác nhận mật khẩu
      if (formData.newPassword !== formData.confirmPassword) {
        setErrorMessage("Mật khẩu xác nhận không khớp");
        setIsSaving(false);
        return;
      }

      // Kiểm tra mật khẩu có ít nhất 8 ký tự
      if (formData.newPassword.length < 8) {
        setErrorMessage("Mật khẩu phải trên 8 ký tự, bao gồm chữ và số");
        setIsSaving(false);
        return;
      }

      // Gọi API đổi mật khẩu sử dụng strapi client
      const data = await strapi.auth.updatePassword(
        user?.email,
        formData.newPassword,
        formData.confirmPassword
      );

      setSuccessMessage("Đổi mật khẩu thành công");
      setTimeout(() => {
        onClose();
      }, 1500);
    } catch (error) {
      console.error("Lỗi đổi mật khẩu:", error);
      setErrorMessage(error.message || "Có lỗi xảy ra. Vui lòng thử lại sau");
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div
      id="password-modal-backdrop"
      className="fixed inset-0 z-50 bg-[#000000] bg-opacity-50 backdrop-blur-[2px] flex items-center justify-center"
      onClick={handleOutsideClick}
    >
      <div className="bg-[#FFFFFF] rounded-2xl max-w-[514px] w-full mx-4 overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between px-6 pt-6 pb-5 border-b border-[#E9EAEB]">
          <h2 className="text-xl text-center items-center font-semibold text-[#181D27]">
            Đổi mật khẩu
          </h2>
          <button
            onClick={onClose}
            className="text-[#717680] hover:text-[#181D27] transition-colors justify-end"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="25"
              viewBox="0 0 24 25"
              fill="none"
            >
              <path
                d="M18 6.5L6 18.5M6 6.5L18 18.5"
                stroke="#A4A7AE"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>

        {/* Form Content */}
        <div className="px-6 py-6 max-sm:max-h-[50vh] overflow-y-auto">
          <div className="space-y-6">
            {/* Mật khẩu hiện tại */}
            <div className="">
              <TextField
                label="Mật khẩu hiện tại"
                name="currentPassword"
                type="password"
                value={formData.currentPassword}
                onChange={handleChange}
                className="flex w-full"
                placeholder="Nhập mật khẩu hiện tại"
                required
                error={
                  errorMessage && !formData.currentPassword
                    ? "Vui lòng nhập mật khẩu hiện tại"
                    : ""
                }
              />
            </div>

            {/* Mật khẩu mới */}
            <div className="">
              <TextField
                label="Mật khẩu mới"
                type="password"
                name="newPassword"
                value={formData.newPassword}
                onChange={handleChange}
                placeholder="Nhập mật khẩu mới"
                required
                error={
                  errorMessage && !formData.newPassword
                    ? "Vui lòng nhập mật khẩu mới"
                    : ""
                }
              />
              <div className="mt-1 text-xs text-[#717680]">
                Mật khẩu phải trên 8 ký tự, bao gồm chữ và số
              </div>
            </div>

            {/* Xác nhận mật khẩu mới */}
            <div className="">
              <TextField
                label="Xác nhận mật khẩu mới"
                type="password"
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleChange}
                placeholder="Nhập mật khẩu mới"
                required
                error={
                  errorMessage && !formData.confirmPassword
                    ? "Vui lòng xác nhận mật khẩu mới"
                    : formData.newPassword !== formData.confirmPassword &&
                      formData.confirmPassword
                    ? "Mật khẩu xác nhận không khớp"
                    : ""
                }
              />
            </div>
          </div>
        </div>

        {/* Hiển thị thông báo lỗi hoặc thành công */}
        {(errorMessage || successMessage) && (
          <div
            className={`px-6 py-2 ${
              errorMessage ? "text-[#F04438]" : "text-[#027A48]"
            } text-sm font-medium`}
          >
            {errorMessage || successMessage}
          </div>
        )}

        {/* Footer */}
        <div className="p-6 border-t border-[#E9EAEB] flex justify-end">
          <Button
            onClick={handleSaveChanges}
            disabled={isSaving}
            variant="primary"
          >
            {isSaving ? (
              <ClipLoader color="#ffffff" size={20} />
            ) : (
              "Lưu thay đổi"
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PasswordChangeModal;
