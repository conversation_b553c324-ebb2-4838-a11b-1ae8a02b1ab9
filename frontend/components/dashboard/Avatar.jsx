"use client";

import React, { useContext, useState, useEffect } from "react";
import Image from "next/image";
import { UserContext } from "../../context/UserProvider";
import AvatarDropdown from "./AvatarDropdown";
import ProfileModal from "./ProfileModal";
import PasswordChangeModal from "./PasswordChangeModal";
import PurchaseHistoryModal from "./PurchaseHistoryModal";
import { useRouter } from "next/navigation";

/**
 * Component Avatar hiển thị ảnh đại diện của người dùng
 * @param {Object} props
 * @param {string} props.alt - Alt text cho ảnh
 * @param {Function} props.onClick - Hàm xử lý khi người dùng click vào avatar
 * @param {string} props.size - Kích thước avatar: "sm" | "md" | "lg"
 * @returns {JSX.Element}
 */
const Avatar = ({ alt = "Avatar người dùng", onClick, size = "md" }) => {
  // State để quản lý trạng thái hiển thị dropdown
  const [showDropdown, setShowDropdown] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [showChangePasswordModal, setShowChangePasswordModal] = useState(false);
  const [showPurchaseHistoryModal, setShowPurchaseHistoryModal] =
    useState(false);
  const router = useRouter();

  // Lấy thông tin người dùng từ UserContext
  const { user } = useContext(UserContext);

  // Lấy avatar URL từ thông tin người dùng
  const avatarSrc = user && user.avatarUrl ? user.avatarUrl : "";

  // Kiểm tra device là mobile/tablet hay desktop
  useEffect(() => {
    if (typeof window !== "undefined") {
      const checkIfMobile = () => {
        setIsMobile(window.innerWidth < 960); // 960px là breakpoint cho tablet theo Tailwind
      };

      // Kiểm tra lúc mount component
      checkIfMobile();

      // Kiểm tra khi resize window
      window.addEventListener("resize", checkIfMobile);

      // Cleanup
      return () => window.removeEventListener("resize", checkIfMobile);
    }
  }, []);

  // Kích thước dựa theo size
  const sizeClasses = {
    sm: "w-8 h-8",
    md: "w-10 h-10",
    lg: "w-16 h-16",
  };

  const sizeClass = sizeClasses[size] || sizeClasses.md;

  // Avatar mặc định khi không có src
  const DefaultAvatar = () => (
    <div
      className={`bg-[#F5F5F5] rounded-full flex items-center justify-center ${sizeClass}`}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
      >
        <path
          d="M16.6654 17.5C16.6654 16.337 16.6654 15.7555 16.5218 15.2824C16.1987 14.217 15.365 13.3834 14.2997 13.0602C13.8265 12.9167 13.245 12.9167 12.082 12.9167H7.91537C6.7524 12.9167 6.17091 12.9167 5.69775 13.0602C4.63241 13.3834 3.79873 14.217 3.47556 15.2824C3.33203 15.7555 3.33203 16.337 3.33203 17.5M13.7487 6.25C13.7487 8.32107 12.0698 10 9.9987 10C7.92763 10 6.2487 8.32107 6.2487 6.25C6.2487 4.17893 7.92763 2.5 9.9987 2.5C12.0698 2.5 13.7487 4.17893 13.7487 6.25Z"
          stroke="#717680"
          strokeWidth="1.66667"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </div>
  );

  // Xử lý click vào avatar
  const handleClick = () => {
    if (isMobile) {
      // Trên mobile/tablet: chuyển hướng đến trang tài khoản

      router.push("/tai-khoan");
    } else {
      // Trên desktop: hiển thị dropdown hoặc gọi onClick nếu được cung cấp
      setShowDropdown(!showDropdown);
      if (onClick) {
        onClick();
      }
    }
  };

  // Xử lý đóng dropdown
  const handleCloseDropdown = () => {
    setShowDropdown(false);
  };

  // Xử lý mở modal hồ sơ
  const handleOpenProfileModal = () => {
    setShowProfileModal(true);
  };

  // Xử lý đóng modal hồ sơ
  const handleCloseProfileModal = () => {
    setShowProfileModal(false);
  };

  // Xử lý mở modal đổi mật khẩu
  const handleOpenPasswordModal = () => {
    setShowChangePasswordModal(true);
  };

  // Xử lý đóng modal đổi mật khẩu
  const handleClosePasswordModal = () => {
    setShowChangePasswordModal(false);
  };

  // Xử lý mở modal lịch sử mua hàng
  const handleOpenPurchaseHistoryModal = () => {
    setShowPurchaseHistoryModal(true);
  };

  // Xử lý đóng modal lịch sử mua hàng
  const handleClosePurchaseHistoryModal = () => {
    setShowPurchaseHistoryModal(false);
  };

  // Tên hiển thị của người dùng (nếu có)
  const userName = user
    ? user.fullname || user.email || "Người dùng"
    : "Người dùng";

  return (
    <>
      <button
        onClick={handleClick}
        className="rounded-full overflow-hidden focus:outline-none"
        aria-label={`Avatar của ${userName}`}
        title={userName}
      >
        {avatarSrc ? (
          <div className={`relative ${sizeClass}`}>
            <Image
              src={avatarSrc}
              alt={alt || `Avatar của ${userName}`}
              fill
              className="object-cover rounded-full"
            />
          </div>
        ) : (
          <DefaultAvatar />
        )}
      </button>

      {/* Chỉ hiển thị dropdown trên desktop */}
      {!isMobile && (
        <AvatarDropdown
          isOpen={showDropdown}
          onClose={handleCloseDropdown}
          onOpenProfile={handleOpenProfileModal}
          onOpenPasswordChange={handleOpenPasswordModal}
          onOpenPurchaseHistory={handleOpenPurchaseHistoryModal}
        />
      )}

      {/* Di chuyển modals lên đây */}
      <ProfileModal
        isOpen={showProfileModal}
        onClose={handleCloseProfileModal}
      />
      <PasswordChangeModal
        isOpen={showChangePasswordModal}
        onClose={handleClosePasswordModal}
      />
      <PurchaseHistoryModal
        isOpen={showPurchaseHistoryModal}
        onClose={handleClosePurchaseHistoryModal}
      />
    </>
  );
};

export default Avatar;
