"use client";

import React, { useState, useContext, useEffect } from "react";
import { UserContext } from "../../context/UserProvider";
import Image from "next/image";
import { ClipLoader } from "react-spinners";
import TextField from "../TextField";
import RadioGroup from "../RadioGroup";
import Button from "../Button";

/**
 * Component hiển thị popup thông tin cá nhân
 * @param {Object} props
 * @param {boolean} props.isOpen - Trạng thái mở của popup
 * @param {Function} props.onClose - Hàm xử lý khi đóng popup
 * @returns {JSX.Element}
 */
const ProfileModal = ({ isOpen, onClose }) => {
  const { user, updateUser } = useContext(UserContext);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [isEditing, setIsEditing] = useState(true);
  const [gender, setGender] = useState(user?.gender === false ? "Nữ" : "Nam");
  const [formData, setFormData] = useState({
    fullname: user?.fullname || "",
    date: user?.date || "",
    gender: gender === "Nam" ? true : false,
    phone: user?.phone || "",
    email: user?.email || "",
  });

  useEffect(() => {
    if (user) {
      const genderString = user.gender === false ? "Nữ" : "Nam";
      setGender(genderString);
      setFormData({
        fullname: user.fullname || "",
        date: user.date || "",
        gender: genderString === "Nam" ? true : false,
        phone: user.phone || "",
        email: user.email || "",
      });
    }
  }, [user, isOpen]);

  if (!isOpen) return null;

  // Xử lý khi click bên ngoài modal
  const handleOutsideClick = (e) => {
    if (e.target.id === "profile-modal-backdrop") {
      onClose();
    }
  };

  // Xử lý khi thay đổi input
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Xử lý khi thay đổi giới tính - cập nhật cả state gender và formData
  const handleGenderChange = (selectedGender) => {
    setGender(selectedGender);
    setFormData((prev) => ({
      ...prev,
      gender: selectedGender === "Nam" ? true : false,
    }));
  };

  // Xử lý khi lưu thông tin
  const handleSaveChanges = async () => {
    setIsSaving(true);
    setErrorMessage("");
    setSuccessMessage("");

    try {
      // Kiểm tra các trường bắt buộc
      if (!formData.fullname || !formData.email) {
        setErrorMessage("Vui lòng điền đầy đủ họ tên và email");
        return;
      }

      // Gọi API cập nhật thông tin
      const result = await updateUser(formData);

      if (result.success) {
        setSuccessMessage("Cập nhật thông tin thành công");
        setTimeout(() => {
          onClose();
        }, 1500);
      } else {
        setErrorMessage(
          result.error || "Cập nhật thất bại. Vui lòng thử lại sau"
        );
      }
    } catch (error) {
      console.error("Lỗi cập nhật thông tin:", error);
      setErrorMessage("Có lỗi xảy ra. Vui lòng thử lại sau");
    } finally {
      setIsSaving(false);
    }
  };

  // Xử lý khi click vào thay đổi avatar
  const handleChangeAvatar = () => {
    // Đây sẽ là nơi để mở file picker
  };

  // Xử lý khi xóa avatar
  const handleDeleteAvatar = () => {};

  return (
    <div
      id="profile-modal-backdrop"
      className="fixed inset-0 z-50 bg-[#000000] bg-opacity-50 backdrop-blur-[2px] flex items-center justify-center"
      onClick={handleOutsideClick}
    >
      <div className="bg-[#FFFFFF] rounded-2xl max-w-[514px] w-full mx-4 overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between px-6 pt-6 pb-5 border-b border-[#E9EAEB]">
          <h2 className="text-xl text-center items-center font-semibold text-[#181D27]">
            Thông tin cá nhân
          </h2>
          <button
            onClick={onClose}
            className="text-[#717680] hover:text-[#181D27] transition-colors justify-end"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="25"
              viewBox="0 0 24 25"
              fill="none"
            >
              <path
                d="M18 6.5L6 18.5M6 6.5L18 18.5"
                stroke="#A4A7AE"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>

        {/* Form Content */}
        <div className="px-6 py-6 max-sm:max-h-[50vh] overflow-y-auto">
          <div className="space-y-6">
            {/* Họ và tên */}
            <div className="">
              <TextField
                label="Họ và tên"
                name="fullname"
                value={formData.fullname}
                onChange={handleChange}
                className="flex w-full"
                placeholder="Nhập họ và tên"
                required
                error={
                  errorMessage && !formData.fullname
                    ? "Vui lòng nhập họ và tên"
                    : ""
                }
              />
            </div>

            {/* Ngày tháng năm sinh */}
            <div className="">
              <TextField
                label="Ngày tháng năm sinh"
                type="date"
                name="date"
                value={formData.date}
                onChange={handleChange}
                required
              />
            </div>

            {/* Giới tính */}
            <div className="w-full sm:w-1/2">
              <RadioGroup
                label="Giới tính"
                options={[
                  { value: "Nam", label: "Nam" },
                  { value: "Nữ", label: "Nữ" },
                ]}
                value={gender}
                onChange={handleGenderChange}
                required
              />
            </div>

            {/* Số điện thoại */}
            <div className="">
              <TextField
                label="Số điện thoại"
                type="tel"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                placeholder="Nhập số điện thoại"
                required
              />
            </div>

            {/* Ảnh đại diện */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-[#414651] mb-2">
                Ảnh đại diện
              </label>
              <div className="flex  justify-between ">
                <div className="relative ">
                  {user?.avatarUrl ? (
                    <Image
                      src={user.avatarUrl}
                      alt="Avatar"
                      fill
                      className="rounded-full object-cover"
                    />
                  ) : (
                    <div className="bg-[#F5F5F5] rounded-full p-4">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="32"
                        height="33"
                        viewBox="0 0 32 33"
                        fill="none"
                      >
                        <path
                          d="M26.6654 28.5C26.6654 26.6392 26.6654 25.7089 26.4357 24.9518C25.9186 23.2473 24.5848 21.9134 22.8802 21.3963C22.1232 21.1667 21.1928 21.1667 19.332 21.1667H12.6654C10.8046 21.1667 9.87424 21.1667 9.11718 21.3963C7.41264 21.9134 6.07875 23.2473 5.56168 24.9518C5.33203 25.7089 5.33203 26.6392 5.33203 28.5M21.9987 10.5C21.9987 13.8137 19.3124 16.5 15.9987 16.5C12.685 16.5 9.9987 13.8137 9.9987 10.5C9.9987 7.18629 12.685 4.5 15.9987 4.5C19.3124 4.5 21.9987 7.18629 21.9987 10.5Z"
                          stroke="#717680"
                          strokeWidth="2.66667"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </div>
                  )}
                </div>
                <div className="flex gap-4 items-start">
                  <button
                    onClick={handleChangeAvatar}
                    className="text-[#198C43] text-sm font-semibold"
                  >
                    Thay đổi
                  </button>
                  <button
                    onClick={handleDeleteAvatar}
                    className="text-[#A4A7AE] text-sm font-semibold "
                  >
                    Xoá
                  </button>
                </div>
              </div>
            </div>

            {/* Email */}
            <div className="mb-6">
              <TextField
                label="Email"
                type="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                disabled={true}
                placeholder="<EMAIL>"
                required
              />
            </div>
          </div>
        </div>

        {/* Hiển thị thông báo lỗi hoặc thành công */}
        {(errorMessage || successMessage) && (
          <div
            className={`px-6 py-2 ${
              errorMessage ? "text-[#F04438]" : "text-[#027A48]"
            } text-sm font-medium`}
          >
            {errorMessage || successMessage}
          </div>
        )}

        {/* Footer */}
        <div className="p-6 border-t border-[#E9EAEB] flex justify-end">
          <Button
            onClick={handleSaveChanges}
            disabled={isSaving}
            variant="primary"
          >
            {isSaving ? (
              <ClipLoader color="#ffffff" size={20} />
            ) : (
              "Lưu thay đổi"
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ProfileModal;
