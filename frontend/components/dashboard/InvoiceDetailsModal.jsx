"use client";

import React, { useEffect, useRef } from "react";
import Button from "../Button";
import StatusActivationBadge from "./StatusActivationBadge";
import BadgeCourse from "./BadgeCourse";
/**
 * Component hiển thị chi tiết hóa đơn
 * @param {Object} props
 * @param {boolean} props.isOpen - Trạng thái mở của modal
 * @param {Function} props.onClose - Hàm xử lý khi đóng modal
 * @param {Object} props.orderInfo - Thông tin đơn hàng
 * @returns {JSX.Element}
 */
const InvoiceDetailsModal = ({ isOpen, onClose, orderInfo }) => {
  const modalRef = useRef(null);

  // Focus trap & close on ESC
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (e) => {
      if (e.key === "Escape") onClose();
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [isOpen, onClose]);

  // Nếu modal đóng, không hiển thị gì
  if (!isOpen || !orderInfo) return null;

  // Format thời gian thanh toán
  const paymentTime = orderInfo.order_date;
  const formattedPaymentTime = paymentTime
    ? new Date(paymentTime).toLocaleString("vi-VN", {
        hour: "2-digit",
        minute: "2-digit",
        day: "2-digit",
        month: "2-digit",
        year: "numeric",
      })
    : "";

  // Thông tin khóa học
  const courseName = orderInfo.course?.title || "Khóa học";

  // Kiểm tra trạng thái kích hoạt
  const isActivated = orderInfo?.activation_codes?.[0]?.discord_status;

  // Mã kích hoạt
  const activationCode = orderInfo?.activation_codes?.[0]?.code || "1234ACB";

  // Mã đơn hàng
  const orderCode = orderInfo.payos_order_code;
  // Thông tin người dùng
  const userInfo = {
    name: orderInfo.users_permissions_user.fullname,
    phone: orderInfo.users_permissions_user.phone,
    email: orderInfo.users_permissions_user.email,
  };

  // Địa chỉ giao tài liệu nếu có
  const deliveryAddress = orderInfo.delivery_address;
  // Thông tin gói đăng ký
  const packageType = orderInfo.course_tier?.tier_type || "Gói 1 năm";

  // Thông tin giảm giá
  const discountAmount = orderInfo.discount_amount || 0;
  const totalAmount = orderInfo.total_amount || 0;

  // Lấy giá gốc từ course_tier.price nếu có, nếu không thì tính từ total_amount + discount_amount
  const originalPrice =
    orderInfo.course_tier?.price ||
    (discountAmount > 0 ? totalAmount + discountAmount : totalAmount);
  const formattedPrice = originalPrice.toLocaleString("vi-VN") + "đ";

  // Format giảm giá
  const formattedDiscount =
    discountAmount > 0 ? `-${discountAmount.toLocaleString("vi-VN")}đ` : "-0đ";
  const handleBackButton = () => {
    onClose();
  };

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-[#000000] bg-opacity-60 px-4"
      tabIndex={-1}
      aria-modal="true"
      role="dialog"
      onClick={onClose}
    >
      <div
        className="bg-[#FFFFFF] rounded-2xl shadow-xl w-full max-w-[514px] relative "
        ref={modalRef}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header with Back and Close buttons */}
        <div className="flex items-center justify-between border-b border-[#E9EAEB] p-4">
          <button
            className="text-[#717680]"
            onClick={handleBackButton}
            aria-label="Quay lại"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
            >
              <path
                d="M19 12H5M5 12L12 19M5 12L12 5"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
          <h2 className="text-lg font-semibold flex-1 text-center">
            Lịch sử mua
          </h2>
          <button
            className="text-[#717680]"
            onClick={onClose}
            aria-label="Đóng"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
            >
              <path
                d="M18 6L6 18M6 6L18 18"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>

        {/* Invoice Content */}
        <div className="px-6 py-8 space-y-8 max-sm:max-h-[60vh] overflow-y-auto">
          {/* Activation Code */}
          <div className="space-y-[6px] flex flex-col">
            <h3 className="text-base text-[#717680] font-semibold ">
              Mã kích hoạt
            </h3>
            <div className="flex items-center gap-2">
              <span className="text-base text-[#181D27] font-semibold">
                {activationCode}
              </span>
              <StatusActivationBadge isActivated={isActivated} />
            </div>
          </div>

          {/* Order Info */}
          <div className="space-y-[6px] ">
            <div className="flex gap-2">
              <h3 className="text-[#181D27] text-base font-semibold">
                Mã đơn hàng
              </h3>
              <span className="text-base text-[#181D27] font-semibold">
                #{orderCode}
              </span>
            </div>
            <div className="flex flex-col sm:flex-row sm:items-center gap-2">
              <p className="text-base text-[#535862] font-normal">
                Thanh toán lúc {formattedPaymentTime}
              </p>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="4"
                height="4"
                viewBox="0 0 4 4"
                fill="none"
                className="hidden sm:block"
              >
                <circle cx="2" cy="2" r="2" fill="#A4A7AE" />
              </svg>
              <p className="text-base text-[#535862] font-normal">Quét mã QR</p>
            </div>
          </div>

          {/* Personal Info */}
          <div className="space-y-[6px]">
            <h3 className="text-[#181D27] text-base font-semibold">
              Thông tin cá nhân
            </h3>
            <div className="flex flex-col">
              <p className="text-base text-[#535862] font-normal">
                {userInfo.name}
              </p>
              <p className="text-base text-[#535862] font-normal">
                {userInfo.phone}
              </p>
              <p className="text-base text-[#535862] font-normal">
                {userInfo.email}
              </p>
            </div>
          </div>

          {/* Delivery Address */}
          {deliveryAddress && (
            <div className="space-y-[6px] ">
              <h3 className="text-base text-[#181D27] font-semibold">
                Tài liệu giao tới
              </h3>
              <p className="text-base text-[#535862] font-normal">
                {deliveryAddress}
              </p>
            </div>
          )}

          {/* Course Info */}
          <div className="space-y-[6px]">
            <div className="flex items-center justify-between py-4 border-y border-[#E9EAEB]">
              <div className="flex  gap-2">
                <h3 className="text-base text-[#181D27] font-semibold">
                  {courseName}
                </h3>
                <BadgeCourse plan={packageType} type="Default" size="sm" />
              </div>
              <p className="hidden sm:block text-base text-[#181D27] font-semibold">
                {totalAmount.toLocaleString("vi-VN")}đ
              </p>
            </div>
            <div className="flex flex-col border-b border-[#E9EAEB] pb-4">
              <div className="flex items-center justify-between">
                <span className="text-base text-[#535862] font-normal">
                  Học phí
                </span>
                <span className="text-base text-[#535862] font-normal">
                  {formattedPrice}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-base text-[#535862] font-normal">
                  Ưu đãi
                </span>
                <span
                  className={`text-base font-normal ${
                    discountAmount > 0 ? "text-green-600" : "text-[#535862]"
                  }`}
                >
                  {formattedDiscount}
                </span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-base text-[#181D27] font-semibold">
                Tổng cộng
              </span>
              <span className="text-base text-[#181D27] font-semibold">
                {totalAmount.toLocaleString("vi-VN")}đ
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InvoiceDetailsModal;
