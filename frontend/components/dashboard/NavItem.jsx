"use client";

import Link from "next/link";
import React, { cloneElement } from "react";

const NavItem = ({ href, icon, text, isActive = false }) => {
  // Thêm màu sắc cho icon khi hover/active
  const styledIcon = React.isValidElement(icon)
    ? cloneElement(icon, {
        className: `${
          isActive
            ? "text-[#198C43]"
            : "text-[#414651] group-hover:text-[#198C43]"
        }`,
        // Thêm stroke và fill theo màu khi hover/active
        stroke: isActive ? "currentColor" : "inherit",
      })
    : icon;

  return (
    <Link
      href={href}
      className={`
        flex items-center  px-3 py-2 rounded-lg gap-2 transition-colors duration-200 group
        ${
          isActive
            ? " text-[#198C43] hover:bg-[#FAFAFA]" // Trạng thái được chọn và hover khi được chọn
            : "text-[#414651] hover:bg-[#FAFAFA] hover:text-[#198C43]" // Trạng thái mặc định và hover
        }
      `}
    >
      <span
        className={`${
          isActive
            ? "text-[#198C43]"
            : "text-[#414651] group-hover:text-[#198C43]"
        }`}
      >
        {styledIcon}
      </span>
      <span className="font-medium">{text}</span>
    </Link>
  );
};

export default NavItem;
