"use client";

import React, { useState, useContext, useEffect } from "react";
import { usePathname } from "next/navigation";
import Image from "next/image";
import NavItem from "../dashboard/NavItem";
import BadgeCourse from "../dashboard/BadgeCourse";
import NotificationBell from "../dashboard/NotificationBell";
import Avatar from "../dashboard/Avatar";
import { UserContext } from "../../context/UserProvider";
import { useRouter } from "next/navigation";
import Bar3Icon from "@/components/icons/Bar3Icon";

// Dashboard layout với sidebar và content area
export default function DashboardLayout({ children }) {
  const [collapsed, setCollapsed] = useState(false);
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);
  const [isChangePasswordModalOpen, setIsChangePasswordModalOpen] =
    useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [courseName, setCourseName] = useState("");
  const [planType, setPlanType] = useState("");
  const [isExpand, setIsExpand] = useState(true);
  const pathname = usePathname();
  const { user, completedOrderInfo } = useContext(UserContext);
  const router = useRouter();
  // Kiểm tra thiết bị là mobile/tablet hay desktop
  useEffect(() => {
    if (typeof window !== "undefined") {
      const checkIsMobile = () => {
        setIsMobile(window.innerWidth < 960); // 960px là breakpoint cho tablet
        if (window.innerWidth >= 960 && window.innerWidth < 1024) {
          setIsExpand(false);
        }
      };

      // Kiểm tra lúc mount component
      checkIsMobile();

      // Kiểm tra khi resize window
      window.addEventListener("resize", checkIsMobile);

      // Cleanup
      return () => window.removeEventListener("resize", checkIsMobile);
    }
  }, []);

  // Lấy thông tin khóa học từ cookies hoặc context
  useEffect(() => {
    try {
      // Ưu tiên lấy từ context
      if (completedOrderInfo) {
        const courseData = completedOrderInfo.course;
        const courseTierData = completedOrderInfo.course_tier;

        if (courseData?.title) {
          setCourseName(courseData.title);
        }

        if (courseTierData?.tier_type) {
          setPlanType(courseTierData.tier_type);
        }
      }
    } catch (error) {
      console.error("Lỗi khi đọc thông tin khóa học:", error);
    }
  }, [completedOrderInfo]);

  // Xử lý khi click vào Avatar
  const handleAvatarClick = () => {
    if (isMobile) {
    } else {
      setIsProfileModalOpen(true);
      setIsChangePasswordModalOpen(true);
    }
  };
  // Danh sách menu của trang quản trị
  const menuItems = [
    { name: "Xem video", path: "/quan-ly/xem-video", icon: "video" },
    { name: "Bài tập", path: "/quan-ly/bai-tap", icon: "task" },
    { name: "Khu tập luyện", path: "/quan-ly/khu-tap-luyen", icon: "practice" },
    { name: "Thành tích", path: "/quan-ly/thanh-tich", icon: "achievement" },
  ];

  const expandMenu = () => {
    setIsExpand(!isExpand);
  }

  // Render icon dựa trên tên
  const renderIcon = (iconName) => {
    switch (iconName) {
      case "video":
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            className="transition-colors duration-200"
          >
            <path
              d="M7.91667 7.47111C7.91667 7.07337 7.91667 6.8745 7.99978 6.76348C8.07222 6.66673 8.18309 6.6062 8.30365 6.59759C8.44199 6.58771 8.60927 6.69524 8.94384 6.91032L12.8777 9.43921C13.168 9.62585 13.3131 9.71917 13.3633 9.83783C13.4071 9.9415 13.4071 10.0585 13.3633 10.1622C13.3131 10.2808 13.168 10.3742 12.8777 10.5608L8.94384 13.0897C8.60927 13.3048 8.44199 13.4123 8.30365 13.4024C8.18309 13.3938 8.07222 13.3333 7.99978 13.2365C7.91667 13.1255 7.91667 12.9266 7.91667 12.5289V7.47111Z"
              stroke="currentColor"
              strokeWidth="1.75"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M2.5 6.5C2.5 5.09987 2.5 4.3998 2.77248 3.86502C3.01217 3.39462 3.39462 3.01217 3.86502 2.77248C4.3998 2.5 5.09987 2.5 6.5 2.5H13.5C14.9001 2.5 15.6002 2.5 16.135 2.77248C16.6054 3.01217 16.9878 3.39462 17.2275 3.86502C17.5 4.3998 17.5 5.09987 17.5 6.5V13.5C17.5 14.9001 17.5 15.6002 17.2275 16.135C16.9878 16.6054 16.6054 16.9878 16.135 17.2275C15.6002 17.5 14.9001 17.5 13.5 17.5H6.5C5.09987 17.5 4.3998 17.5 3.86502 17.2275C3.39462 16.9878 3.01217 16.6054 2.77248 16.135C2.5 15.6002 2.5 14.9001 2.5 13.5V6.5Z"
              stroke="currentColor"
              strokeWidth="1.75"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        );
      case "task":
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            className="transition-colors duration-200"
          >
            <path
              d="M4.16797 7.4987H7.5013M4.16797 9.9987H7.5013M4.16797 12.4987H7.5013M12.5013 7.4987H15.8346M12.5013 9.9987H15.8346M12.5013 12.4987H15.8346M1.66797 5.23679C1.66797 4.18482 2.49707 3.33203 3.51982 3.33203H8.14945C9.1722 3.33203 10.0013 4.18482 10.0013 5.23679C10.0013 4.18482 10.8304 3.33203 11.8532 3.33203H16.4828C17.5055 3.33203 18.3346 4.18482 18.3346 5.23679V14.7606C18.3346 15.8126 17.5055 16.6654 16.4828 16.6654H11.8532C10.8304 16.6654 10.0013 15.8126 10.0013 14.7606C10.0013 15.8126 9.1722 16.6654 8.14945 16.6654H3.51982C2.49707 16.6654 1.66797 15.8126 1.66797 14.7606V5.23679Z"
              stroke="currentColor"
              strokeWidth="1.75"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        );
      case "practice":
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            className="transition-colors duration-200"
          >
            <path
              d="M4.59073 12.082V13.4706C4.58075 13.8453 4.9196 14.1563 5.34785 14.1654H6.86329C7.29154 14.1563 7.63039 13.8453 7.62041 13.4706L7.62041 6.52682C7.63039 6.15209 7.29154 5.84113 6.86329 5.83203H5.34785C4.9196 5.84113 4.58075 6.15209 4.59073 6.52682V7.91536M4.59073 12.082H2.42531C1.99706 12.0729 1.6582 11.762 1.66818 11.3872L1.66818 8.61016C1.6582 8.23542 1.99706 7.92446 2.42531 7.91536L4.59073 7.91536M4.59073 12.082L4.59073 7.91536M15.4131 7.91536L15.4131 12.082M7.62043 9.9987L12.3822 9.9987M14.6548 14.1654H13.1393C12.7111 14.1563 12.3722 13.8453 12.3822 13.4706L12.3822 6.52682C12.3722 6.15209 12.7111 5.84113 13.1393 5.83203L14.6548 5.83203C15.083 5.84113 15.4219 6.15209 15.4119 6.52682V7.91536H17.5761C17.782 7.91946 17.9776 7.99498 18.1198 8.12529C18.262 8.25559 18.3392 8.43002 18.3344 8.61016V11.3872C18.3444 11.762 18.0055 12.0729 17.5773 12.082H15.4119V13.4706C15.4219 13.8453 15.083 14.1563 14.6548 14.1654Z"
              stroke="currentColor"
              strokeWidth="1.75"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        );
      case "achievement":
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            className="transition-colors duration-200"
          >
            <g clipPath="url(#clip0_1625_907)">
              <path
                d="M10.0013 12.5013C7.23988 12.5013 5.0013 10.2627 5.0013 7.5013V2.87167C5.0013 2.5268 5.0013 2.35436 5.05156 2.21629C5.13581 1.98481 5.31815 1.80247 5.54962 1.71822C5.6877 1.66797 5.86013 1.66797 6.20501 1.66797H13.7976C14.1425 1.66797 14.3149 1.66797 14.453 1.71822C14.6845 1.80247 14.8668 1.98481 14.951 2.21629C15.0013 2.35436 15.0013 2.5268 15.0013 2.87167V7.5013C15.0013 10.2627 12.7627 12.5013 10.0013 12.5013ZM10.0013 12.5013V15.0013M15.0013 3.33464H17.0846C17.4729 3.33464 17.6671 3.33464 17.8202 3.39807C18.0244 3.48265 18.1866 3.64488 18.2712 3.84907C18.3346 4.00221 18.3346 4.19635 18.3346 4.58464V5.0013C18.3346 5.77628 18.3346 6.16377 18.2495 6.48168C18.0183 7.34441 17.3444 8.01828 16.4817 8.24945C16.1638 8.33464 15.7763 8.33464 15.0013 8.33464M5.0013 3.33464H2.91797C2.52968 3.33464 2.33554 3.33464 2.1824 3.39807C1.97821 3.48265 1.81598 3.64488 1.7314 3.84907C1.66797 4.00221 1.66797 4.19635 1.66797 4.58464V5.0013C1.66797 5.77628 1.66797 6.16377 1.75315 6.48168C1.98432 7.34441 2.65819 8.01828 3.52092 8.24945C3.83884 8.33464 4.22633 8.33464 5.0013 8.33464M6.20501 18.3346H13.7976C14.0021 18.3346 14.168 18.1688 14.168 17.9643C14.168 16.3279 12.8414 15.0013 11.205 15.0013H8.7976C7.1612 15.0013 5.83464 16.3279 5.83464 17.9643C5.83464 18.1688 6.00046 18.3346 6.20501 18.3346Z"
                stroke="currentColor"
                strokeWidth="1.75"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </g>
            <defs>
              <clipPath id="clip0_1625_907">
                <rect width="20" height="20" fill="white" />
              </clipPath>
            </defs>
          </svg>
        );
      default:
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            className="transition-colors duration-200"
          >
            <g clipPath="url(#clip0_1625_907)">
              <path
                d="M10.0013 12.5013C7.23988 12.5013 5.0013 10.2627 5.0013 7.5013V2.87167C5.0013 2.5268 5.0013 2.35436 5.05156 2.21629C5.13581 1.98481 5.31815 1.80247 5.54962 1.71822C5.6877 1.66797 5.86013 1.66797 6.20501 1.66797H13.7976C14.1425 1.66797 14.3149 1.66797 14.453 1.71822C14.6845 1.80247 14.8668 1.98481 14.951 2.21629C15.0013 2.35436 15.0013 2.5268 15.0013 2.87167V7.5013C15.0013 10.2627 12.7627 12.5013 10.0013 12.5013ZM10.0013 12.5013V15.0013M15.0013 3.33464H17.0846C17.4729 3.33464 17.6671 3.33464 17.8202 3.39807C18.0244 3.48265 18.1866 3.64488 18.2712 3.84907C18.3346 4.00221 18.3346 4.19635 18.3346 4.58464V5.0013C18.3346 5.77628 18.3346 6.16377 18.2495 6.48168C18.0183 7.34441 17.3444 8.01828 16.4817 8.24945C16.1638 8.33464 15.7763 8.33464 15.0013 8.33464M5.0013 3.33464H2.91797C2.52968 3.33464 2.33554 3.33464 2.1824 3.39807C1.97821 3.48265 1.81598 3.64488 1.7314 3.84907C1.66797 4.00221 1.66797 4.19635 1.66797 4.58464V5.0013C1.66797 5.77628 1.66797 6.16377 1.75315 6.48168C1.98432 7.34441 2.65819 8.01828 3.52092 8.24945C3.83884 8.33464 4.22633 8.33464 5.0013 8.33464M6.20501 18.3346H13.7976C14.0021 18.3346 14.168 18.1688 14.168 17.9643C14.168 16.3279 12.8414 15.0013 11.205 15.0013H8.7976C7.1612 15.0013 5.83464 16.3279 5.83464 17.9643C5.83464 18.1688 6.00046 18.3346 6.20501 18.3346Z"
                stroke="currentColor"
                strokeWidth="1.75"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </g>
            <defs>
              <clipPath id="clip0_1625_907">
                <rect width="20" height="20" fill="white" />
              </clipPath>
            </defs>
          </svg>
        );
    }
  };

  return (
    <div className="flex h-screen bg-[#FFFFFF] ">
      {/* Sidebar - menu */}
      <div className={`md:block hidden bg-[#FFFFFF] border-r border-[#E9EAEB] p-4 ${isExpand && 'w-[296px]'}`}
      >
        <div className="flex  items-center justify-between border-b border-[#E9EAEB] pb-4 ">
          <div className={!isExpand ? 'hidden' : 'flex items-center justify-center'}>
            <Image
                src="/images/homepage/logo.png"
                alt="Ông Bà Dạy Hóa"
                width={87}
                height={32}
                className="py-2"
            />
          </div>
          <div className="cursor-pointer text-center" onClick={expandMenu}>
            <Bar3Icon className={'m-auto'}></Bar3Icon>
          </div>
        </div>
        <div className={`mt-3 flex flex-col gap-2`}>
          {menuItems.map((item) => (
              <NavItem
                  key={item.path}
                  href={item.path}
                  icon={renderIcon(item.icon)}
                  text={isExpand ? item.name : ''}
                  isActive={pathname === item.path}
              />
          ))}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        {/* Header */}
        <header className="md:bg-[#FFFFFF] bg-[#299D55] border-b border-[#E9EAEB] flex items-center justify-between px-8 py-4">
          <div className="flex flex-col gap-[2px]">
            <div className="text-lg text-[#FFF] md:text-[#181D27] font-semibold">
              Xin chào,{" "}
              {user
                ? (() => {
                    const nameParts = user.fullname?.trim().split(" ") || [];
                    return nameParts.length > 0
                      ? nameParts[nameParts.length - 1]
                      : "Học viên";
                  })()
                : "Học viên"}{" "}
              👋
            </div>
            <div className="flex items-center gap-2">
              <div className="flex items-center">
                <span className="text-[#FFF] md:text-[#535862] text-xs leading-[18px] font-medium">
                  {courseName}
                </span>
              </div>

              <div className="md:hidden flex items-center justify-center">
                <BadgeCourse plan={planType} type="Neutral" size="sm" />
              </div>
              <div className="hidden md:flex items-center justify-center">
                <BadgeCourse plan={planType} type="Default" size="sm" />
              </div>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <NotificationBell
              onClick={() => console.log("Notification clicked")}
            />
            <Avatar size="md" onClick={handleAvatarClick} />
          </div>
        </header>

        {/* Page Content */}
        <main className="py-8  md:px-8 px-4 ">{children}</main>

        {/* Mobile/Tablet Bottom Navigation */}
        {isMobile && (
          <nav className="fixed bottom-0 left-0 right-0 bg-[#FFFFFF] border-t border-[#E9EAEB] z-50">
            <div className="flex justify-around items-center p-4">
              {menuItems.map((item) => (
                <a
                  key={item.path}
                  href={item.path}
                  className={`flex flex-col items-center justify-center  ${
                    pathname === item.path ? "text-[#299D55]" : "text-[#717680]"
                  }`}
                >
                  <div className="mb-1">
                    {React.cloneElement(renderIcon(item.icon), {
                      className: `${
                        pathname === item.path
                          ? "text-[#299D55]"
                          : "text-[#717680]"
                      }`,
                    })}
                  </div>
                  <span className="text-xs font-medium">{item.name}</span>
                </a>
              ))}
            </div>
          </nav>
        )}
      </div>
    </div>
  );
}
