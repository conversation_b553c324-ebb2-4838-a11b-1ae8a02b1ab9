import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
/** @type {import('next').NextConfig} */
const nextConfig = {
    images: {
        remotePatterns: [
            {
                protocol: process.env.NEXT_PUBLIC_STRAPI_PROTOCOL,
                hostname: process.env.NEXT_PUBLIC_STRAPI_HOST,
                port: process.env.NEXT_PUBLIC_STRAPI_PORT,
                pathname: '/uploads/**',
            },
        ],
    },
    webpack: (config) => {
        config.resolve.alias['@'] = __dirname;
        return config;
    },
    // serverActions: {
    //     bodySizeLimit: '50mb',
    // },
    reactStrictMode: true,
};

export default nextConfig;
