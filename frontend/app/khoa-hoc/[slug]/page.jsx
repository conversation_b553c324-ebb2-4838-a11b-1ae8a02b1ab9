"use client";

import React, { useState, useEffect, useRef, useContext } from "react";
import Image from "next/image";
import Link from "next/link";
import { useParams, useSearchParams, useRouter } from "next/navigation";
import Button from "../../../components/Button";
import GradeSelect from "../../../components/GradeSelect";
import strapi from "../../api/strapi";
import LoginPopup from "../../../components/LoginPopup";
import VideoPopup from "../../../components/VideoPopup";
import { UserContext } from "../../../context/UserProvider";

const CourseDetailPage = () => {
  const params = useParams();
  const slug = params.slug;
debugger

  // const { slug } = useParams();
  const searchParams = useSearchParams();
  // const gradeId = searchParams.get("grade");
  const router = useRouter();

  const [course, setCourse] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showGradeSelect, setShowGradeSelect] = useState(false);
  const [openChapters, setOpenChapters] = useState({});
  const [expandedFaqs, setExpandedFaqs] = useState([]);
  const [features, setFeatures] = useState([]);
  const [isLoginPopupOpen, setIsLoginPopupOpen] = useState(false);
  const [isVideoPopupOpen, setIsVideoPopupOpen] = useState(false);
  const [currentVideoUrl, setCurrentVideoUrl] = useState("");
  const { user } = useContext(UserContext);

  const registerCardRef = useRef(null);

  useEffect(() => {
    if (searchParams.size > 0) {
      setError("Không tìm thấy khóa học này.");
      setLoading(false);
      return;
    }
    const fetchCourseData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Lấy thông tin khóa học theo slug
        const courseData = await strapi.courses.getCourseBySlug(slug, null);

        if (!courseData) {
          setError("Không tìm thấy khóa học này.");
          setLoading(false);
          return;
        }

        setCourse(courseData);
      } catch (err) {
        console.error("Lỗi khi lấy thông tin khóa học:", err);
        setError("Không thể tải thông tin khóa học. Vui lòng thử lại sau.");
      } finally {
        setLoading(false);
      }
    };

    fetchCourseData();
  }, [slug]);

  const toggleChapter = (index) => {
    setOpenChapters((prev) => ({
      ...prev,
      [index]: !prev[index],
    }));
  };

  const handleExpandFaq = (index) => {
    setExpandedFaqs((prev) => {
      const isExpanded = prev.includes(index);
      return isExpanded ? prev.filter((i) => i !== index) : [...prev, index];
    });
  };

  const getRemainingDays = (endDate) => {
    if (!endDate) return null;
    const end = new Date(endDate);
    const now = new Date();
    const timeDiff = end - now;
    const daysRemaining = Math.ceil(timeDiff / (1000 * 60 * 60 * 24)); // Chuyển đổi milliseconds sang ngày
    return daysRemaining >= 0 ? daysRemaining : 0; // Trả về 0 nếu đã hết thời gian
  };

  const handleScrollToRegister = () => {
    registerCardRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const handleOpenVideoDemo = (e, videoUrl) => {
    e.preventDefault();
    e.stopPropagation();
    setCurrentVideoUrl(videoUrl);
    setIsVideoPopupOpen(true);
  };

  const isCourseExpired = (endDate) => {
    if (!endDate) return false; // Nếu không có endDate, coi như không hết hạn
    const now = new Date();
    return new Date(endDate) < now; // Trả về true nếu endDate đã qua
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#DD2590]"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center">
          <h2 className="text-2xl font-bold text-[#1D2939] mb-4 text-center">{error}</h2>
          <Link href="/">
            <Button>Về trang chủ</Button>
          </Link>
        </div>
      </div>
    );
  }

  const formatCourseData = () => {
    if (!course) return {};

    return {
      id: course.id,
      title: course.title,
      description: course.description,
      url_video_demo: course.url_video_demo,
      // image: course.attributes.image?.data?.attributes?.url
      //   ? `${process.env.NEXT_PUBLIC_STRAPI_URL}${course.attributes.image.data.attributes.url}`
      //   : "/images/course-placeholder.jpg",

      duration: course.duration,
      price_description: course.price_description,
      slug: course.slug,
      gradeId: course.gradeId,

      chapters:
        course.chapters?.map((chapter) => ({
          id: chapter.id,
          title: chapter.title,
          basicKnowlegdeCount: chapter.basicKnowlegdeCount,
          exerciseCount: chapter.exerciseCount,
          order: chapter.order,
          video_demo: chapter.video_demo,

          knowledgeSections:
            chapter.knowledgeSections?.map((section) => ({
              id: section.id,
              title: section.title,
              order: section.order,
              items: section.items || [],
            })) || [],

          exercises:
            chapter.exercises?.map((exercise) => ({
              id: exercise.id,
              title: exercise.title,
              order: exercise.order,
              content: exercise.content,
            })) || [],
        })) || [],

      course_tiers:
        course.course_tiers?.map((tier) => ({
          id: tier.id,
          tier_name: tier.tier_name,
          discount_percent: tier.discount_percent,
          discount_endDate: tier.discount_endDate,
          price: tier.price,
          is_default: tier.is_default,
          total_lessons: tier.total_lessons,
          discord_role_id: tier.discord_role_id,
          discord_channel_url: tier.discord_channel_url,
          tier_type: tier.tier_type,
          startDate: tier.startDate,
          endDate: tier.endDate,
        })) || [],

      faqs:
        course.faqs?.map((faq) => ({
          id: faq.id,
          question: faq.question,
          answer: faq.answer,
          order: faq.order,
        })) || [],

      features:
        course.features?.map((feature) => ({
          id: feature.id,
          icon: feature.icon,
          title: feature.title,
          description: feature.description,
        })) || [],

      class_schedules:
        course.class_schedules?.map((schedule) => ({
          id: schedule.id,
          name: schedule.name,
          grade: schedule.grade,
          date: schedule.date,
          schedule: schedule.schedule,
        })) || [],
    };
  };

  const courseData = formatCourseData();

  const defaultTier = courseData.course_tiers?.find((tier) => tier.is_default);
  const trialTier = courseData.course_tiers?.find((tier) => !tier.is_default);

  // Mapping gradeId to grade string
  const gradeMapping = {
    1: "Lớp 10",
    2: "Lớp 11",
    3: "Lớp 12"
  };

  // Filter lịch học theo grade của khóa học hiện tại
  const currentGrade = gradeMapping[courseData.gradeId];
  const filteredSchedules = courseData.class_schedules?.filter(
    schedule => schedule.grade === currentGrade
  ) || [];

  const remainingDays = getRemainingDays(defaultTier?.discount_endDate);

  // Hàm xử lý đăng ký khóa học
  const handleRegister = (tier) => {
    const courseParam = `${courseData.slug}-${tier.tier_type}`;

    // Tạo đối tượng chứa các thông tin cần truyền
    const courseDataToSend = {
      id: courseData.id,
      title: courseData.title,
      duration: courseData.duration,
      total_lessons: tier.total_lessons,
      slug: courseData.slug,
      tier_type: tier.tier_type,
      tier_id: tier.id,
      price: tier.price - (tier.price * (tier.discount_percent || 0)) / 100,
    };

    // Lưu dữ liệu vào localStorage thay vì truyền qua URL
    localStorage.setItem("coursedata", JSON.stringify(courseDataToSend));
    if (!user) {
      setIsLoginPopupOpen(true);
      return;
    }
    // Tạo URL đơn giản
    const url = `/thanh-toan?${courseParam}`;

    router.push(url);
  };

  if (!course && !showGradeSelect) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-[#1D2939] mb-4">
            Không tìm thấy khóa học này.
          </h2>
          <Button onClick={() => setShowGradeSelect(true)}>
            Chọn khối lớp khác
          </Button>
        </div>
      </div>
    );
  }

  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';

  let courseJsonLd = null;
  if (courseData && courseData.slug) { // Kiểm tra courseData và slug
    const defaultTier = courseData.course_tiers?.find((tier) => tier.is_default); // Lấy default tier để có thể thêm thông tin giá

    courseJsonLd = {
      '@context': 'https://schema.org',
      '@type': 'Course',
      name: courseData.title,
      description: courseData.description ? courseData.description.substring(0, 250) : '',
      url: `${siteUrl}/khoa-hoc/${courseData.slug}`,
      provider: {
        '@type': 'Organization',
        name: 'Ông Ba Dạy Hóa',
        sameAs: siteUrl,
      },
      // Thêm image nếu có, ví dụ:
      // image: courseData.image ? `${courseData.image}` : `${siteUrl}/images/metadata-img/course-og.jpg`, // Giả sử courseData.image là URL tuyệt đối
      courseCode: courseData.id, // Mã khóa học nếu có
      // educationalCredentialAwarded: "Certificate", // Nếu có chứng chỉ
    };

    // Thêm thông tin offer nếu có defaultTier
    if (defaultTier) {
      const offer = {
        '@type': 'Offer',
        category: 'Paid', // Hoặc 'Free'
        price: defaultTier.price - (defaultTier.price * (defaultTier.discount_percent || 0)) / 100,
        priceCurrency: 'VND',
        url: `${siteUrl}/khoa-hoc/${courseData.slug}`, // URL để đăng ký/mua
        availability: isCourseExpired(defaultTier.endDate) ? 'https://schema.org/Discontinued' : 'https://schema.org/InStock',
        validFrom: defaultTier.startDate ? new Date(defaultTier.startDate).toISOString() : undefined,
        validThrough: defaultTier.endDate ? new Date(defaultTier.endDate).toISOString() : undefined,
      };
      courseJsonLd.offers = offer;
      // Thêm hasCourseInstance cho rich results
      courseJsonLd.hasCourseInstance = {
        '@type': 'CourseInstance',
        courseMode: 'online',
        courseWorkload: defaultTier.total_lessons ? `P${defaultTier.total_lessons}H` : undefined,
        startDate: defaultTier.startDate ? new Date(defaultTier.startDate).toISOString() : undefined,
        endDate: defaultTier.endDate ? new Date(defaultTier.endDate).toISOString() : undefined,
        offers: offer,
      };
    }
  }

  return (
    <>
      {courseJsonLd && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(courseJsonLd) }}
        />
      )}
      <div className="min-h-screen bg-white px-4 md:py-16 py-8">
        {showGradeSelect && (
          <GradeSelect
            onClose={() => setShowGradeSelect(false)}
            courseSlug={slug}
          />
        )}

        <div className="max-w-[1200px] mx-auto">
          <div className="flex flex-col md:flex-row gap-10">
            <div className="md:w-[794px] w-full flex flex-col">
              <div className="flex flex-col gap-5 w-full md:order-1 order-2">
                <h1 className="md:w-[590px] text-4xl leading-[44px] tracking-[-0.72px] font-bold text-[#181D27]">
                  {courseData.title}
                </h1>
                <p className="text-xl leading-[30px] text-[#535862] font-normal">
                  {courseData.description}
                </p>

                <div className="flex sm:flex-row flex-col gap-4 w-full">
                  <div className="flex flex-col gap-1 px-6 py-4 rounded-2xl border border-[#E9EAEB] w-full">
                    <p className="text-sm text-[#535862] font-normal">
                      Khai giảng ngày
                    </p>
                    <div className="flex gap-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="20"
                        height="20"
                        viewBox="0 0 20 20"
                        fill="none"
                      >
                        <path
                          d="M17.5 8.33366H2.5M13.3333 1.66699V5.00033M6.66667 1.66699V5.00033M6.5 18.3337H13.5C14.9001 18.3337 15.6002 18.3337 16.135 18.0612C16.6054 17.8215 16.9878 17.439 17.2275 16.9686C17.5 16.4339 17.5 15.7338 17.5 14.3337V7.33366C17.5 5.93353 17.5 5.23346 17.2275 4.69868C16.9878 4.22828 16.6054 3.84583 16.135 3.60614C15.6002 3.33366 14.9001 3.33366 13.5 3.33366H6.5C5.09987 3.33366 4.3998 3.33366 3.86502 3.60614C3.39462 3.84583 3.01217 4.22828 2.77248 4.69868C2.5 5.23346 2.5 5.93353 2.5 7.33366V14.3337C2.5 15.7338 2.5 16.4339 2.77248 16.9686C3.01217 17.439 3.39462 17.8215 3.86502 18.0612C4.3998 18.3337 5.09987 18.3337 6.5 18.3337Z"
                          stroke="#181D27"
                          strokeWidth="1.66667"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                      <p className="text-base text-[#181D27] font-medium">
                        {courseData.course_tiers[0]?.startDate
                          ? new Date(
                              courseData.course_tiers[0].startDate
                            ).toLocaleDateString("vi-VN")
                          : "04/08/2025"}
                      </p>
                    </div>
                  </div>

                  <div className="flex flex-col gap-1 px-6 py-4 rounded-2xl border border-[#E9EAEB] w-full">
                    <p className="text-sm text-[#535862] font-normal">
                      Số buổi livestream
                    </p>
                    <div className="flex gap-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="20"
                        height="20"
                        viewBox="0 0 20 20"
                        fill="none"
                      >
                        <path
                          d="M9.99999 16.6663H4.33332C3.3999 16.6663 2.93319 16.6663 2.57667 16.4847C2.26307 16.3249 2.0081 16.0699 1.84831 15.7563C1.66666 15.3998 1.66666 14.9331 1.66666 13.9997V5.99967C1.66666 5.06625 1.66666 4.59954 1.84831 4.24302C2.0081 3.92942 2.26307 3.67445 2.57667 3.51466C2.93319 3.33301 3.3999 3.33301 4.33332 3.33301H4.66666C6.5335 3.33301 7.46692 3.33301 8.17996 3.69632C8.80717 4.0159 9.3171 4.52583 9.63668 5.15304C9.99999 5.86608 9.99999 6.7995 9.99999 8.66634M9.99999 16.6663V8.66634M9.99999 16.6663H15.6667C16.6001 16.6663 17.0668 16.6663 17.4233 16.4847C17.7369 16.3249 17.9919 16.0699 18.1517 15.7563C18.3333 15.3998 18.3333 14.9331 18.3333 13.9997V5.99967C18.3333 5.06625 18.3333 4.59954 18.1517 4.24302C17.9919 3.92942 17.7369 3.67445 17.4233 3.51466C17.0668 3.33301 16.6001 3.33301 15.6667 3.33301H15.3333C13.4665 3.33301 12.5331 3.33301 11.8201 3.69632C11.1928 4.0159 10.6829 4.52583 10.3633 5.15304C9.99999 5.86608 9.99999 6.7995 9.99999 8.66634"
                          stroke="#181D27"
                          strokeWidth="1.67"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                      <p className="text-base text-[#181D27] font-medium">
                        {defaultTier?.total_lessons} buổi
                      </p>
                    </div>
                  </div>

                  <div className="flex flex-col gap-1 px-6 py-4 rounded-2xl border border-[#E9EAEB] w-full">
                    <p className="text-sm text-[#535862] font-normal">
                      Thời gian mỗi buổi
                    </p>
                    <div className="flex gap-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="20"
                        height="20"
                        viewBox="0 0 20 20"
                        fill="none"
                      >
                        <g clipPath="url(#clip0_555_8433)">
                          <path
                            d="M10 5.00033V10.0003L13.3334 11.667M18.3334 10.0003C18.3334 14.6027 14.6024 18.3337 10 18.3337C5.39765 18.3337 1.66669 14.6027 1.66669 10.0003C1.66669 5.39795 5.39765 1.66699 10 1.66699C14.6024 1.66699 18.3334 5.39795 18.3334 10.0003Z"
                            stroke="#181D27"
                            strokeWidth="1.67"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </g>
                        <defs>
                          <clipPath id="clip0_555_8433">
                            <rect width="20" height="20" fill="white" />
                          </clipPath>
                        </defs>
                      </svg>
                      <p className="text-base text-[#181D27] font-medium">
                        {courseData.duration}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="w-full my-12 max-md:mt-0 md:order-2 order-1">
                <div className="relative overflow-hidden rounded-lg">
                  <iframe
                    className="rounded-lg w-full
                    h-[223.722px] aspect-[375/223.72] sm:h-[382.415px] sm:aspect-[641/382.41]
                    custom:h-[388.381px] custom:aspect-[651/388.38]
                    xl:h-[473.693px] xl:aspect-[794/473.69]
                    self-stretch"
                    src={courseData.url_video_demo}
                    title="YouTube video player"
                    frameBorder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                    referrerPolicy="strict-origin-when-cross-origin"
                    allowFullScreen
                  ></iframe>
                </div>
              </div>

              <div
                ref={registerCardRef}
                className="md:hidden flex gap-6 order-4 md:flex-row flex-col py-12"
              >
                {/* Card học cả năm */}
                {!isCourseExpired(defaultTier?.endDate) && (
                  <div className="bg-[#F5FFFA] flex flex-col gap-4 p-6 border-2 border-[#45BF76] rounded-2xl">
                    <div className="flex flex-col gap-0.5">
                      <h3 className="text-lg text-[#181D27] font-semibold">
                        Đăng ký học cả năm
                      </h3>
                      <p className="text-sm text-[#535862] font-normal">
                        Đăng ký cho cả năm học, với ưu đãi có hạn. Hãy bắt đầu
                        trải nghiệm một năm học hóa thật thú vị!
                      </p>
                    </div>
                    <div className="flex flex-col gap-1">
                      <h1 className="text-4xl text-[#198C43] leading-[44px] tracking-[-0.72px] font-semibold">
                        {(
                          defaultTier?.price -
                          (defaultTier?.price * defaultTier?.discount_percent) /
                            100
                        ).toLocaleString("vi-VN")}
                        đ
                      </h1>
                      {remainingDays > 0 && (
                        <div className="flex gap-2">
                          <span className="bg-[#F04438] rounded-[6px] text-[#FFFFFF] text-sm font-semibold py-0.5 px-1.5">
                            Giảm {defaultTier?.discount_percent}%
                          </span>
                          <p className="text-sm text-[#535862] font-normal line-through">
                            {(defaultTier?.price).toLocaleString("vi-VN")}đ
                          </p>
                        </div>
                      )}
                      {remainingDays > 0 ? (
                        <p className="text-sm text-[#D92D20] font-normal">
                          Còn{" "}
                          <span className="font-semibold">
                            {remainingDays} ngày
                          </span>{" "}
                          ưu đãi sẽ kết thúc
                        </p>
                      ) : (
                        <p className="hidden text-sm text-[#D92D20] font-normal">
                          Ưu đãi đã kết thúc
                        </p>
                      )}
                    </div>
                    <Button
                      variant="primary"
                      className="w-full"
                      onClick={() => handleRegister(defaultTier)}
                    >
                      Đăng ký ngay
                    </Button>
                  </div>
                )}

                {/* Card học thử */}
                {!isCourseExpired(trialTier?.endDate) && (
                  <div className="flex flex-col gap-4 p-6 border border-[#E9EAEB] rounded-2xl">
                    <div className="flex flex-col gap-0.5">
                      <h3 className="text-lg text-[#181D27] font-semibold">
                        Trải nghiệm học thử 1 tháng
                      </h3>
                      <p className="text-sm text-[#535862] font-normal">
                        Mỗi học sinh chỉ có thể đăng ký một lần để trải nghiệm
                        toàn bộ tính năng của khoá học
                      </p>
                    </div>
                    <div className="flex flex-col gap-1">
                      <h1 className="text-4xl text-[#198C43] leading-[44px] tracking-[-0.72px] font-semibold">
                        {(trialTier?.price).toLocaleString("vi-VN")}đ
                      </h1>
                      <p className="text-sm text-[#D92D20] font-normal">
                        Chỉ diễn ra đến{" "}
                        <span className="font-semibold">
                          {new Date(trialTier.endDate).toLocaleDateString(
                            "vi-VN"
                          )}
                        </span>
                      </p>
                    </div>
                    <Button
                      variant="secondaryColor"
                      className="w-full"
                      onClick={() => handleRegister(trialTier)}
                    >
                      Trải nghiệm ngay
                    </Button>
                  </div>
                )}
              </div>

              {/* Lịch học online - Hiển thị theo grade */}
              {filteredSchedules && filteredSchedules.length > 0 && (
                <div className="md:order-3 order-5 mb-[40px]">
                  <h2 className="text-2xl font-bold mb-6">Lịch học Livestream</h2>
                  <div className="flex flex-col border border-[#E4E7EC] rounded-2xl">
                    {filteredSchedules.map((schedule, index) => (
                      <div
                        key={schedule.id}
                        className="border-b border-[#E4E7EC] overflow-hidden"
                      >
                        <div className="w-full flex items-center justify-between px-6 py-4 gap-6">
                          <div className="flex items-center gap-6">
                            <div className="flex flex-col items-start w-full">
                              <h3 className="text-lg font-semibold text-[#1D2939] text-start">
                                {schedule.name}
                              </h3>
                              <p className="text-sm text-[#667085] text-start">
                                Khai giảng: {schedule.date ? new Date(schedule.date).toLocaleDateString("vi-VN") : "Sẽ thông báo"} • {schedule.grade}
                              </p>
                            </div>
                          </div>
                        </div>

                        <div className="p-4 bg-[#FAFAFA] border-t border-[#E4E7EC]">
                          {schedule.schedule && Array.isArray(schedule.schedule) && schedule.schedule.length > 0 ? (
                            <div className="space-y-4">
                              <p className="text-sm font-medium text-[#374151]">Lịch học chi tiết:</p>
                              <div className="grid md:grid-cols-2 gap-4">
                                {schedule.schedule.map((item, itemIndex) => (
                                  <div key={itemIndex} className="flex items-start gap-3 p-4 bg-[#F9FAFB] rounded-lg border border-[#E9EAEB]">
                                    <div className="w-2 h-2 bg-[#059669] rounded-full mt-2 flex-shrink-0"></div>
                                    <span className="text-sm text-[#535862]">{item}</span>
                                  </div>
                                ))}
                              </div>
                            </div>
                          ) : (
                            <p className="text-sm text-[#667085]">Lịch học sẽ được cập nhật sớm</p>
                          )}
                          
                          <div className="mt-4 p-4 bg-[#EFF6FF] rounded-lg border border-[#BFDBFE]">
                            <p className="text-sm text-[#1E40AF] flex items-start gap-2">
                              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none" className="mt-0.5 flex-shrink-0">
                                <path d="M10 6.66699V10.0003L12.5 12.5003M18.3333 10.0003C18.3333 14.6027 14.6024 18.3337 10 18.3337C5.39763 18.3337 1.66667 14.6027 1.66667 10.0003C1.66667 5.39795 5.39763 1.66699 10 1.66699C14.6024 1.66699 18.3333 5.39795 18.3333 10.0003Z" stroke="#1E40AF" strokeWidth="1.66667" strokeLinecap="round" strokeLinejoin="round"/>
                              </svg>
                              Lịch học có thể thay đổi tùy theo tình hình thực tế. Thông tin chi tiết sẽ được gửi qua email sau khi đăng ký.
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="md:order-4 order-6">
                <h2 className="text-2xl font-bold mb-6">Nội dung khoá học</h2>
                <div className="flex flex-col border border-[#E4E7EC] rounded-2xl">
                  {courseData.chapters
                    .sort((a, b) => a.order - b.order)
                    .map((chapter, index) => (
                      <div
                        key={chapter.id}
                        className="border-b border-[#E4E7EC] overflow-hidden"
                      >
                        <button
                          onClick={() => toggleChapter(index)}
                          className="w-full flex items-center justify-between px-6 py-4 gap-6"
                        >
                          <div className="flex items-center gap-6">
                            <div
                              className={`transform transition-transform ${
                                openChapters[index] ? "rotate-180" : ""
                              }`}
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="24"
                                height="25"
                                viewBox="0 0 24 25"
                                fill="none"
                              >
                                <path
                                  d="M6 9.69336L12 15.6934L18 9.69336"
                                  stroke="#535862"
                                  strokeWidth="2"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                />
                              </svg>
                            </div>
                            <div className="flex flex-col items-start w-full ">
                              <h3 className="text-lg font-semibold text-[#1D2939] text-start">
                                {chapter.title}
                              </h3>
                              <p className="sm:block hidden text-sm text-[#667085] text-start">
                                {chapter.basicKnowlegdeCount} kiến thức cần nắm
                                · {chapter.exerciseCount} dạng bài tập
                              </p>
                              <div className="sm:hidden text-sm text-[#667085] text-start flex gap-2">
                                <div className="flex items-center gap-1 text-sm font-normal">
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="16"
                                    height="17"
                                    viewBox="0 0 16 17"
                                    fill="none"
                                  >
                                    <path
                                      d="M8.00065 14.2712H3.46732C2.72058 14.2712 2.34721 14.2712 2.062 14.1258C1.81111 13.998 1.60714 13.794 1.47931 13.5431C1.33398 13.2579 1.33398 12.8846 1.33398 12.1378V5.73783C1.33398 4.99109 1.33398 4.61772 1.47931 4.3325C1.60714 4.08162 1.81111 3.87765 2.062 3.74982C2.34721 3.60449 2.72058 3.60449 3.46732 3.60449H3.73398C5.22746 3.60449 5.97419 3.60449 6.54463 3.89514C7.04639 4.1508 7.45434 4.55875 7.71 5.06052C8.00065 5.63095 8.00065 6.37769 8.00065 7.87116M8.00065 14.2712V7.87116M8.00065 14.2712H12.534C13.2807 14.2712 13.6541 14.2712 13.9393 14.1258C14.1902 13.998 14.3942 13.794 14.522 13.5431C14.6673 13.2579 14.6673 12.8846 14.6673 12.1378V5.73783C14.6673 4.99109 14.6673 4.61772 14.522 4.3325C14.3942 4.08162 14.1902 3.87765 13.9393 3.74982C13.6541 3.60449 13.2807 3.60449 12.534 3.60449H12.2673C10.7738 3.60449 10.0271 3.60449 9.45668 3.89514C8.95491 4.1508 8.54696 4.55875 8.2913 5.06052C8.00065 5.63095 8.00065 6.37769 8.00065 7.87116"
                                      stroke="#414651"
                                      strokeWidth="1.25"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                    />
                                  </svg>
                                  {chapter.basicKnowlegdeCount}
                                </div>
                                <div className="flex items-center justify-center">
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="2"
                                    height="3"
                                    viewBox="0 0 2 3"
                                    fill="none"
                                  >
                                    <circle
                                      cx="1"
                                      cy="1.9375"
                                      r="1"
                                      fill="#535862"
                                    />
                                  </svg>
                                </div>

                                <div className="flex items-center gap-1 text-sm font-normal">
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="16"
                                    height="17"
                                    viewBox="0 0 16 17"
                                    fill="none"
                                  >
                                    <g clipPath="url(#clip0_602_8422)">
                                      <path
                                        d="M9.33268 2.4502V5.20389C9.33268 5.57726 9.33268 5.76394 9.40534 5.90655C9.46926 6.03199 9.57125 6.13398 9.69669 6.19789C9.8393 6.27056 10.026 6.27056 10.3993 6.27056H13.153M10.666 9.60384H5.33268M10.666 12.2705H5.33268M6.66602 6.93717H5.33268M9.33268 2.27051H5.86602C4.74591 2.27051 4.18586 2.27051 3.75803 2.48849C3.38171 2.68024 3.07575 2.9862 2.884 3.36253C2.66602 3.79035 2.66602 4.3504 2.66602 5.47051V12.4038C2.66602 13.5239 2.66602 14.084 2.884 14.5118C3.07575 14.8881 3.38171 15.1941 3.75803 15.3859C4.18586 15.6038 4.74591 15.6038 5.86602 15.6038H10.1327C11.2528 15.6038 11.8128 15.6038 12.2407 15.3859C12.617 15.1941 12.9229 14.8881 13.1147 14.5118C13.3327 14.084 13.3327 13.5239 13.3327 12.4038V6.27051L9.33268 2.27051Z"
                                        stroke="#414651"
                                        strokeWidth="1.25"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                      />
                                    </g>
                                    <defs>
                                      <clipPath id="clip0_602_8422">
                                        <rect
                                          width="16"
                                          height="16"
                                          fill="white"
                                          transform="translate(0 0.9375)"
                                        />
                                      </clipPath>
                                    </defs>
                                  </svg>
                                  {chapter.exerciseCount}
                                </div>
                                <div className="flex items-center justify-center">
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="2"
                                    height="3"
                                    viewBox="0 0 2 3"
                                    fill="none"
                                  >
                                    <circle
                                      cx="1"
                                      cy="1.9375"
                                      r="1"
                                      fill="#535862"
                                    />
                                  </svg>
                                </div>
                                {chapter.video_demo && (
                                  <div className="">
                                    <button
                                      onClick={(e) =>
                                        handleOpenVideoDemo(
                                          e,
                                          chapter.video_demo
                                        )
                                      }
                                      className="flex items-center gap-1.5 text-[#198C43] text-start"
                                    >
                                      <span className="text-sm font-semibold w-full">
                                        Xem demo buổi học
                                      </span>
                                    </button>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                          <div className="flex-shrink-0 sm:block hidden">
                            {chapter.video_demo && (
                              <button
                                onClick={(e) =>
                                  handleOpenVideoDemo(e, chapter.video_demo)
                                }
                                className="flex items-center gap-1.5 text-[#198C43] text-start"
                              >
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="20"
                                  height="21"
                                  viewBox="0 0 20 21"
                                  fill="none"
                                  className="sm:block hidden"
                                >
                                  <path
                                    d="M10 19.027C14.6024 19.027 18.3334 15.2961 18.3334 10.6937C18.3334 6.09131 14.6024 2.36035 10 2.36035C5.39765 2.36035 1.66669 6.09131 1.66669 10.6937C1.66669 15.2961 5.39765 19.027 10 19.027Z"
                                    stroke="#198C43"
                                    strokeWidth="1.66667"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                  />
                                  <path
                                    d="M7.91669 8.1648C7.91669 7.76706 7.91669 7.56819 7.99981 7.45717C8.07224 7.36041 8.18312 7.29988 8.30367 7.29127C8.44201 7.28139 8.60929 7.38893 8.94386 7.60401L12.8777 10.1329C13.168 10.3195 13.3132 10.4129 13.3633 10.5315C13.4071 10.6352 13.4071 10.7522 13.3633 10.8559C13.3132 10.9745 13.168 11.0678 12.8777 11.2545L8.94386 13.7834C8.60929 13.9984 8.44201 14.106 8.30367 14.0961C8.18312 14.0875 8.07224 14.027 7.99981 13.9302C7.91669 13.8192 7.91669 13.6203 7.91669 13.2226V8.1648Z"
                                    stroke="#198C43"
                                    strokeWidth="1.66667"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                  />
                                </svg>
                                <span className="text-sm font-semibold w-full">
                                  Xem demo buổi học
                                </span>
                              </button>
                            )}
                          </div>
                        </button>

                        {openChapters[index] && (
                          <div className="p-4 bg-[#FAFAFA] border-t border-[#E4E7EC]">
                            <div className="mb-6">
                              <div className="flex items-center gap-2 mb-4">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="20"
                                  height="21"
                                  viewBox="0 0 20 21"
                                  fill="none"
                                >
                                  <path
                                    d="M10.0001 17.3597H4.33341C3.39999 17.3597 2.93328 17.3597 2.57676 17.178C2.26316 17.0183 2.00819 16.7633 1.8484 16.4497C1.66675 16.0932 1.66675 15.6265 1.66675 14.693V6.69303C1.66675 5.75961 1.66675 5.2929 1.8484 4.93638C2.00819 4.62278 2.26316 4.36781 2.57676 4.20802C2.93328 4.02637 3.39999 4.02637 4.33341 4.02637H4.66675C6.53359 4.02637 7.46701 4.02637 8.18005 4.38968C8.80726 4.70926 9.31719 5.21919 9.63677 5.8464C10.0001 6.55944 10.0001 7.49286 10.0001 9.3597M10.0001 17.3597V9.3597M10.0001 17.3597H15.6667C16.6002 17.3597 17.0669 17.3597 17.4234 17.178C17.737 17.0183 17.992 16.7633 18.1518 16.4497C18.3334 16.0932 18.3334 15.6265 18.3334 14.693V6.69303C18.3334 5.75961 18.3334 5.2929 18.1518 4.93638C17.992 4.62278 17.737 4.36781 17.4234 4.20802C17.0669 4.02637 16.6002 4.02637 15.6667 4.02637H15.3334C13.4666 4.02637 12.5332 4.02637 11.8201 4.38968C11.1929 4.70926 10.683 5.21919 10.3634 5.8464C10.0001 6.55944 10.0001 7.49286 10.0001 9.3597"
                                    stroke="#414651"
                                    strokeWidth="1.75"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                  />
                                </svg>
                                <h4 className="text-base font-semibold text-[#181D27]">
                                  Kiến thức cần nắm
                                </h4>
                              </div>
                              <div className="flex flex-col gap-2">
                                {chapter.knowledgeSections.map(
                                  (section, sIndex) => (
                                    <div key={sIndex}>
                                      <h5 className="text-base font-semibold text-[#414651] mb-1">
                                        {section.order}. {section.title}
                                      </h5>
                                      <ul className="flex flex-col pl-4">
                                        {section.items.map((item, iIndex) => (
                                          <li
                                            key={iIndex}
                                            className="text-base font-normal text-[#535862]"
                                          >
                                            • {item}
                                          </li>
                                        ))}
                                      </ul>
                                    </div>
                                  )
                                )}
                              </div>
                            </div>

                            <div>
                              <div className="flex items-center gap-2 mb-2">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="20"
                                  height="21"
                                  viewBox="0 0 20 21"
                                  fill="none"
                                >
                                  <path
                                    d="M11.6666 2.58496V6.02708C11.6666 6.49379 11.6666 6.72714 11.7574 6.9054C11.8373 7.0622 11.9648 7.18969 12.1216 7.26958C12.2999 7.36041 12.5332 7.36041 12.9999 7.36041H16.442M13.3333 11.527H6.66659M13.3333 14.8604H6.66659M8.33325 8.19369H6.66659M11.6666 2.36035H7.33325C5.93312 2.36035 5.23306 2.36035 4.69828 2.63284C4.22787 2.87252 3.84542 3.25497 3.60574 3.72538C3.33325 4.26015 3.33325 4.96022 3.33325 6.36035V15.027C3.33325 16.4272 3.33325 17.1272 3.60574 17.662C3.84542 18.1324 4.22787 18.5149 4.69828 18.7545C5.23306 19.027 5.93312 19.027 7.33325 19.027H12.6666C14.0667 19.027 14.7668 19.027 15.3016 18.7545C15.772 18.5149 16.1544 18.1324 16.3941 17.662C16.6666 17.1272 16.6666 16.4272 16.6666 15.027V7.36035L11.6666 2.36035Z"
                                    stroke="#414651"
                                    strokeWidth="1.75"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                  />
                                </svg>
                                <h4 className="text-base font-semibold text-[#181D27]">
                                  Dạng bài tập
                                </h4>
                              </div>
                              <ul className="flex flex-col gap-2">
                                {chapter.exercises.map((exercise, eIndex) => (
                                  <li
                                    key={eIndex}
                                    className="flex text-base font-semibold text-[#414651]"
                                  >
                                    {exercise.title}.
                                    <span className="ml-1">
                                      {exercise.content.map((item, iIndex) => (
                                        <p key={iIndex}>
                                          {item.children[0].text}
                                        </p>
                                      ))}
                                    </span>
                                  </li>
                                ))}
                              </ul>
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                </div>
                {/* Tính năng trong khoá học */}
                <div className="my-[40px] order-7">
                  <h2 className="text-2xl font-semibold text-[#181D27] mb-6">
                    Tính năng trong khoá học
                  </h2>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-y-6 gap-x-8">
                    {courseData.features.map((feature, index) => (
                      <div key={index} className="bg-white">
                        <div className="flex items-center gap-2 mb-2">
                          <span
                            dangerouslySetInnerHTML={{ __html: feature.icon }}
                          />
                          <h3 className="font-semibold text-xl leading-[30px] text-[#181D27]">
                            {feature.title}
                          </h3>
                        </div>
                        <p className="text-base text-[#535862] font-normal">
                          {feature.description}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Câu hỏi thường gặp - Chỉ hiển thị khi có data */}
                {courseData.faqs && courseData.faqs.length > 0 && (
                  <div className="order-8">
                    <h2 className="text-2xl font-semibold mb-6">
                      Câu hỏi thường gặp
                    </h2>
                    <div className="space-y-6">
                      {courseData.faqs.map((faq, index) => (
                      <div
                        key={index}
                        className="border-b border-[#E9EAEB] pb-4 mt-6"
                        onClick={() => {
                          setExpandedFaqs((prev) => {
                            const isExpanded = prev.includes(index);
                            return isExpanded
                              ? prev.filter((i) => i !== index)
                              : [...prev, index];
                          });
                        }}
                      >
                        <div className="flex items-center justify-between cursor-pointer">
                          <h3 className="font-medium text-lg text-[#181D27]">
                            {faq.question}
                          </h3>
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="25"
                            viewBox="0 0 24 25"
                            fill="none"
                            className={`transition-transform duration-200 ${
                              expandedFaqs.includes(index) ? "" : "rotate-180"
                            }`}
                          >
                            <path
                              d="M18 15.6931L12 9.69312L6 15.6931"
                              stroke="#A4A7AE"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        </div>
                        {expandedFaqs.includes(index) && (
                          <p className="text-[#535862] text-base font-normal mt-2">
                            {faq.answer}
                          </p>
                        )}
                      </div>
                    ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Right section - Desktop */}
            <div className="md:block hidden w-[386px]">
              <div className="sticky top-28 space-y-6">
                {/* Card học cả năm */}
                {!isCourseExpired(defaultTier?.endDate) && (
                  <div className="bg-[#F5FFFA] flex flex-col gap-4 p-6 border-2 border-[#45BF76] rounded-2xl">
                    <div className="flex flex-col gap-0.5">
                      <h3 className="text-lg text-[#181D27] font-semibold">
                        Đăng ký học cả năm
                      </h3>
                      <p className="text-sm text-[#535862] font-normal">
                        Đăng ký cho cả năm học, với ưu đãi có hạn. Hãy bắt đầu
                        trải nghiệm một năm học hóa thật thú vị!
                      </p>
                    </div>
                    <div className="flex flex-col gap-1">
                      <h1 className="text-4xl text-[#198C43] leading-[44px] tracking-[-0.72px] font-semibold">
                        {(
                          defaultTier?.price -
                          (defaultTier?.price * defaultTier?.discount_percent) /
                            100
                        ).toLocaleString("vi-VN")}
                        đ
                      </h1>
                      {remainingDays > 0 && (
                        <div className="flex gap-2">
                          <span className="bg-[#F04438] rounded-[6px] text-[#FFFFFF] text-sm font-semibold py-0.5 px-1.5">
                            Giảm {defaultTier?.discount_percent}%
                          </span>
                          <p className="text-sm text-[#535862] font-normal line-through">
                            {(defaultTier?.price).toLocaleString("vi-VN")}đ
                          </p>
                        </div>
                      )}
                      {remainingDays > 0 ? (
                        <p className="text-sm text-[#D92D20] font-normal">
                          Còn{" "}
                          <span className="font-semibold">
                            {remainingDays} ngày
                          </span>{" "}
                          ưu đãi sẽ kết thúc
                        </p>
                      ) : (
                        <p className="hidden text-sm text-[#D92D20] font-normal">
                          Ưu đãi đã kết thúc
                        </p>
                      )}
                    </div>
                    <Button
                      variant="primary"
                      className="w-full"
                      onClick={() => handleRegister(defaultTier)}
                    >
                      Đăng ký ngay
                    </Button>
                  </div>
                )}

                {/* Card học thử */}
                {!isCourseExpired(trialTier?.endDate) && (
                  <div className="flex flex-col gap-4 p-6 border border-[#E9EAEB] rounded-2xl">
                    <div className="flex flex-col gap-0.5">
                      <h3 className="text-lg text-[#181D27] font-semibold">
                        Trải nghiệm học thử 1 tháng
                      </h3>
                      <p className="text-sm text-[#535862] font-normal">
                        Mỗi học sinh chỉ có thể đăng ký một lần để trải nghiệm
                        toàn bộ tính năng của khoá học
                      </p>
                    </div>
                    <div className="flex flex-col gap-1">
                      <h1 className="text-4xl text-[#198C43] leading-[44px] tracking-[-0.72px] font-semibold">
                        {(trialTier?.price).toLocaleString("vi-VN")}đ
                      </h1>
                      <p className="text-sm text-[#D92D20] font-normal">
                        Chỉ diễn ra đến{" "}
                        <span className="font-semibold">
                          {new Date(trialTier.endDate).toLocaleDateString(
                            "vi-VN"
                          )}
                        </span>
                      </p>
                    </div>
                    <Button
                      variant="secondaryColor"
                      className="w-full"
                      onClick={() => handleRegister(trialTier)}
                    >
                      Trải nghiệm ngay
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="md:hidden fixed bottom-0 left-0 right-0 bg-[#FFFFFF] px-4 py-3 border-t border-[#E9EAEB]">
            <Button
              variant="primary"
              className="w-full"
              onClick={handleScrollToRegister}
            >
              Đăng ký
            </Button>
          </div>
        </div>
      </div>

      <LoginPopup
        isOpen={isLoginPopupOpen}
        onClose={() => setIsLoginPopupOpen(false)}
        courseInfo={courseData}
      />

      <VideoPopup
        isOpen={isVideoPopupOpen}
        onClose={() => setIsVideoPopupOpen(false)}
        videoUrl={currentVideoUrl}
      />
    </>
  );
};

export default CourseDetailPage;
