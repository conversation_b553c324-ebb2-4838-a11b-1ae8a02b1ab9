'use client';
import React, {useEffect, useRef, useState} from 'react';
import strapi from "@/app/api/strapi";
import {useNotification} from "@/context/NotificationContext";
import TextField from "@/components/TextField";
import AppLoading from "@/components/AppLoading";
import Button from "@/components/Button";
import Cookies from "universal-cookie";
import Select from "react-select";
import {red} from "next/dist/lib/picocolors";
import TextError from "@/components/TextError";
import {checkValidate} from "@/utils/vadidateUtil";

export default function UploadVideoPage() {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [file, setFile] = useState(null);
  const [thumbnail, setThumbnail] = useState(null);
  const [lstGrades, setLstGrades] = useState([]);
  const [lstChapters, setLstChapters] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [chapters, setChapters] = useState([]);
  const [grades, setGrades] = useState([]);
  const [courses, setCourses] = useState([]);
  const [lstCourses, setLstCourses] = useState([]);
  const [collectionVideos, setCollectionVideos] = useState([]);
  const [errors, setErrors] = useState({});
  const [fileInputKey, setFileInputKey] = useState(Date.now()); // key dùng cho video input
  const [thumbInputKey, setThumbInputKey] = useState(Date.now() + 1); // key cho thumbnail input

  const {showNotification} = useNotification();



  useEffect(() => {
    const fetchGrades = async () => {
      const r = await strapi.grade.getGradeJustChapter();
      const a = r.data || [];
      const tmp = a.map((value) => ({
        ...value,
        value: value.documentId,
        label: value.title
      }));
      setGrades(tmp);
    };
    const fetchCourses = async () => {
      const r = await strapi.courses.getAllCourses();
      const a = r.data || [];
      const tmp = a.map((value) => ({
        ...value,
        value: value.documentId,
        label: value.title
      }));
      setCourses(tmp);
    };

    fetchGrades();
    fetchCourses();
  }, []);

  const cookies = new Cookies();
  let user = cookies.get('user_data');
  if (user && user.isUploadVideo) {

    const resetForm = () => {
      setTitle('');
      setDescription('');
      setFile(null);
      setThumbnail(null);
      setFileInputKey(Date.now());
      setThumbInputKey(Date.now() + 1);
      setLstGrades([]);
      setLstChapters([]);
      // setChapters([]);
      // setGrades([]);
      // setCourses([]);
      setLstCourses([]);
      setCollectionVideos([]);
      setErrors({});
    }
    const handleSubmit = async (e) => {

      e.preventDefault();

      // if (!file) return alert('Chọn file video.');
      setErrors({});
      const validations = [
        { field: 'title', value: title, message: 'Tiêu đề video không được để trống' },
        { field: 'file', value: file, message: 'Vui lòng tải file lên' },
        { field: 'description', value: description, message: 'Mô tả không được để trống' },
        { field: 'grades', value: lstGrades.length > 0, message: 'Vui lòng lựa chọn lớp' }
      ];
      if (checkValidate(validations)) {
        setErrors(checkValidate(validations));
        return;
      }

      // validations.forEach(({ field, value, message }) => {
      //   const isValid = typeof value === 'boolean' ? value : !!value;
      //   if (!isValid) {
      //     currentErrors[field] = message;
      //     pass = false;
      //   }
      // });
      //
      // if (!pass) {
      //   setErrors(currentErrors);
      //   return;
      // }


      debugger
      let formData = new FormData();
      formData.append('title', title);
      formData.append('description', description);
      formData.append('file', file);
      formData.append('thumbnail', thumbnail);
      //vì gửi data qua formData nên phải làm như này
      lstGrades.forEach((value,index) => {
        formData.append('grades[' + index + ']', value.id);
      });
      lstChapters.forEach((value,index) => {
        formData.append('chapters[' + index + ']', value.id);
      });
      collectionVideos.forEach((value,index) => {
        formData.append('collection[' + index + ']', value);
      });

      setIsLoading(true);
      try {
        const res = await strapi.uploadVideo.upload(formData);
        console.log(res);
        if (res && res.success) {
          showNotification({
            type: "success",
            title: "Thành công",
            message: res.message || 'Lưu video thành công',
          });
          resetForm();

          setIsLoading(false);
        } else {
          showNotification({
            type: "error",
            title: "Lỗi",
            message: res.message || 'Có lỗi xảy khi upload video',
            duration: 5000,
          });
          setIsLoading(false);
        }

      } catch (apiError) {
        console.error("API Error:", apiError);
        console.error("Error response:", apiError.response?.data);
        showNotification({
          type: "error",
          title: "Lỗi",
          message:
              apiError.response?.data?.error?.message ||
              "Có lỗi xảy ra khi gửi form",
          duration: 5000,
        });
        setIsLoading(false);
      }
    };
    const classInput = "w-full px-[14px] py-[10px] rounded-lg text-base font-normal transition-all duration-200 border focus:outline-none pr-[14px] border-[#D5D7DA] hover:border-[#45BF76] hover:border-2 text-[#181D27] placeholder:text-[#717680]";
    const handleInputChange = (e) => {
      setLstGrades(e);
      let a = e || [];
      setChapters([]);
      if (a.length === 0) {
        setLstChapters([]);
        return;
      }
      let temp = [];
      a.forEach(value => {
        const transformed = value.course.chapters.map(item => ({...item,
          label: item.title,
          value: item.id
        }));
        temp.push(...transformed);
      })
      setChapters(temp);
    }
    const handleInputCoursesChange = (e) => {
      setLstCourses(e);
      setCollectionVideos([]);
      let a = e || [];
      let tempCollect = [];
      a.forEach(value => {
        tempCollect.push(value.collection_video_id);
      });
      setCollectionVideos(tempCollect);
    }

    return (
        <>
          <AppLoading isLoading={isLoading}></AppLoading>
          <div className="p-6 mx-auto width50">
            <div className="text-center width100">
              <h2 className="text-xl font-bold mb-4">Tải lên video bài học</h2>
            </div>
            <form onSubmit={handleSubmit} className="flex flex-col gap-4">
              <label className="text-sm font-medium text-[#414651]">Tiêu đề video</label>
              <input
                  type="text"
                  placeholder="Tiêu đề video"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  className={classInput}
              />
              <TextError error={errors.title}></TextError>
              <label className="text-sm font-medium text-[#414651]">Video upload</label>
              <input
                  key={fileInputKey}
                  type="file"
                  accept="video/*"
                  onChange={(e) => setFile(e.target.files[0])}
                  className={classInput}
                  // ref={fileInputRef}
              />
              <label className="text-sm font-medium text-[#414651]">Hình ảnh video</label>
              <input
                  key={thumbInputKey}
                  type="file"
                  accept="image/*"
                  onChange={(e) => setThumbnail(e.target.files[0])}
                  className={classInput}
                  // ref={fileInputRef}
              />
              <TextError error={errors.file}></TextError>
              <textarea
                  placeholder="Mô tả video"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  className={classInput}
              />
              <TextError error={errors.description}></TextError>
              <div>
                <Select
                    isMulti
                    options={grades}
                    value={lstGrades}
                    name="grades"
                    onChange={handleInputChange}
                    placeholder="Chọn lớp học..."
                />
              </div>
              <TextError error={errors.grades}></TextError>
              <div>
                <Select
                    isMulti
                    options={chapters}
                    value={lstChapters}
                    onChange={setLstChapters}
                    placeholder="Kiến thức liên quan"
                />
              </div>

              <div>
                <Select
                    isMulti
                    options={courses}
                    value={lstCourses}
                    name="courses"
                    onChange={handleInputCoursesChange}
                    placeholder="Chọn khóa học"
                />
              </div>
              <div className="flex justify-center mt-12">
                <Button variant="secondaryColor">Lưu lại</Button>
              </div>
            </form>
          </div>
        </>

    )
  } else {
    return (
        <div className="w-full h-[300px] flex items-center justify-center text-center">
          <h2 className="text-xl font-bold mb-4">BẠN KHÔNG CÓ QUYỀN THAO TÁC TRÊN CHỨC NĂNG NÀY</h2>
        </div>
    )
  }


}
