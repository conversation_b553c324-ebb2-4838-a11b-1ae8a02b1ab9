"use client";

import DashboardLayout from "../../../components/layouts/DashboardLayout";
import CountdownWidget from "../../../components/dashboard/CountdownWidget";

const targetDate = new Date("2025-07-01");
export default function Dashboard() {
  return (
    <DashboardLayout>
      <div className="flex flex-col items-center justify-center h-full min-h-[calc(100vh-160px)]">
        <div className="bg-[#FFFFFF]  p-8 max-w-md w-full">
          <CountdownWidget
            targetDate={targetDate}
            title="Thời gian khai giảng đếm ngược"
            message={<>Trong lúc đợi... sao không cày view TikTok <br className="hidden sm:inline" /> cho Ông Ba nhỉ? 🤗</>}
            onComplete={() => console.log("Đếm ngược hoàn thành!")}
          />
        </div>
      </div>
    </DashboardLayout>
  );
}
