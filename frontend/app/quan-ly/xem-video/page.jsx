"use client";

import DashboardLayout from "../../../components/layouts/DashboardLayout";
import CountdownWidget from "../../../components/dashboard/CountdownWidget";
import React, {useEffect, useRef, useState} from "react";
import strapi from "@/app/api/strapi";
import Cookies from "universal-cookie";
import {FaPlay, FaSearch} from 'react-icons/fa';
import TextField from "@/components/TextField";
import {useRouter} from "next/navigation";



export default function ViewVideo() {
    const router = useRouter();
    const [videos, setListVideos] = useState([]);
    const [videoOriginal, setListVideoOriginal] = useState([]);
    const [formData, setFormData] = useState({keySearch: ""});
    const [errors, setErrors] = useState({});
    const timeoutRef = useRef(null);
    useEffect(  () => {

        const cookies = new Cookies();
        const completedOrderInfo = cookies.get('completedOrderInfo');
        const collection_video_id = completedOrderInfo.course.collection_video_id;
        const getListVideo = async () => {
            await strapi.quanLy.getVideoUpload(collection_video_id).then(res => {
                if (res.data && res.data.length > 0) {
                    setListVideos(res.data);
                    setListVideoOriginal(res.data);
                }else {
                    setListVideos([]);
                    setListVideoOriginal([]);
                }
            });
        }
        getListVideo();

    }, []);

    const onClickVideo = (video) => {
        debugger
        router.push( window.location.href + '/chi-tiet?videoId=' + video.video_id + '&collectionId=' + video.collection_id);
    }
    const inputFindVideo = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name]: value }));
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
        }
        timeoutRef.current = setTimeout(() => {
            if (value.trim()) {
                const keySearch = value.trim().toLowerCase();
                const matchedVideos = videoOriginal.filter(video =>
                    video.title.toLowerCase().includes(keySearch) ||
                    video.description.toLowerCase().includes(keySearch)
                );
                setListVideos(matchedVideos);
            }else {
                setListVideos(videoOriginal);
            }

        },300);


    }
    return (
    <DashboardLayout>
        <div className="flex flex-col h-full min-h-[calc(100vh-160px)]">
            <div className="bg-white w-full sm:gap-4xl flex flex-col xs:gap-y-3xl">
                <div className="w-full flex xs:flex-col xs:h-fit xs:gap-xl sm:flex-row justify-between sm:gap-xl sm:h-[40px] ">
                    <div className="flex items-center xs:w-full sm:w-1/2 sm:flex-1">
                        <p className="text-primary-900 font-semibold xs:text-xl xs:leading-xl sm:text-display-xs sm:leading-display-xs ">
                            Xem video
                        </p>
                    </div>
                    <div className=" rounded-md xs:w-full xs:p-0  sm:w-[248px] sm:py-md sm:px-lg custom3:w-[296]">
                        <TextField
                            name="keySearch"
                            type="text"
                            value={formData.keySearch}
                            onChange={inputFindVideo}
                            placeholder="Tìm kiếm video"
                            error={errors.keySearch}
                        />
                    </div>
                </div>


                {/* Video đầu tiên */}
                {videos.length > 0 && (
                    <div className="flex flex-col xs:gap-lg sm:flex-row sm:gap-xl cursor-pointer">
                        <div className="relative w-full md:w-1/2 rounded-md">
                            <img
                                src={videos[0].thumbnail}
                                alt="Video Thumbnail"
                                onClick={() => onClickVideo(videos[0])}
                                className="w-full h-full object-cover rounded-md max-h-[333px]"
                            />
                            <div
                                onClick={() => onClickVideo(videos[0])}
                                className="absolute inset-0 flex flex-col justify-center items-center bg-black/30">
                                <button className="text-white/65 text-4xl">
                                    <FaPlay FaPlay className="color-fa-play"/>
                                </button>
                            </div>
                            <div className="absolute top-2 left-2 py-xs px-lg rounded-full border bg-utility-brand-50 border-utility-brand-200">
                                <p className="text-utility-brand-700 text-sm font-medium">Mới nhất</p>
                            </div>
                        </div>
                        <div className="flex flex-col gap-md w-full md:w-1/2">
                            <p className="text-primary-900 font-semibold xs:text-sm xs:leading-sm sm:text-md sm:leading-md md:text-lg md:leading-lg">{videos[0].title}</p>
                            <p className="text-quaternary-500 text-sm font-normal">{new Date(videos[0].updatedAt).toLocaleDateString("vi-VN")}</p>
                            <p className="text-tertiary-600 xs:text-sm font-normal xs:hidden sm:block md:text-md md:leading-md">{videos[0].description}</p>
                        </div>
                    </div>
                )}

                {/* Các video còn lại */}
                <div className=" grid cursor-pointer  xs:gap-x-xl xs:gap-y-3xl xs:grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 sm:gap-y-4xl">
                    {videos.slice(1).map((video, index) => (
                        <div
                            key={index}
                            onClick={() => onClickVideo(video)}
                            className="rounded-md shadow-lg overflow-hidden bg-gradient-to-b from-indigo-200 to-pink-200 xs:h-[261] md:h-[361] custom3:h-[237]"
                        >
                            <div className="relative w-full xs:h-[161px] xs:mb-lg sm:h-[185] md:h-[231] custom3:h-[161]">
                                <img
                                    src={video.thumbnail}
                                    alt="Video Thumbnail"
                                    className="w-full h-full object-cover rounded-md"
                                />
                                <div className="absolute inset-0 flex flex-col justify-center items-center bg-black/30">
                                    <button className="text-white/65 text-4xl">
                                        <FaPlay className="color-fa-play"/>
                                    </button>
                                </div>
                            </div>
                            <div className="pl-xs">
                                <p className="text-primary-900 font-semibold line-clamp-2 xs:text-sm xs:leading-sm xs:mb-xs">{video.title}</p>
                                <p className="text-tertiary-600 font-normal xs:text-sm xs:leading-sm">{new Date(video.updatedAt).toLocaleDateString("vi-VN")}</p>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    </DashboardLayout>

);
}
