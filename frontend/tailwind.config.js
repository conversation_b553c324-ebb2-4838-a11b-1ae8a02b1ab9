/** @type {import('tailwindcss').Config} */
module.exports = {
    content: [
        "./app/**/*.{js,ts,jsx,tsx,mdx}",
        "./app/**/**/*.{js,ts,jsx,tsx,mdx}",
        "./pages/**/*.{js,ts,jsx,tsx,mdx}",
        "./components/**/*.{js,ts,jsx,tsx,mdx}",

        // Or if using `src` directory:
        "./src/**/*.{js,ts,jsx,tsx,mdx}",
    ],
    theme: {
        extend: {
            spacing: {
                'none' : '0px',
                'xxs' : '2px',
                'xs' : '4px',
                'sm' : '6px',
                'md' : '8px',
                'lg' : '12px',
                'xl' : '16px',
                '2xl' : '20px',
                '3xl' : '24px',
                '4xl' : '32px',
                '5xl' : '40px',
                '6xl' : '48px',
                '7xl' : '64px',
                '8xl' : '80px',
                '9xl' : '96px',
                '10xl' : '128px',
                '11xl' : '160px',
            },
            fontSize: {
                'xxs': '10px',
                'xs': '12px',
                'sm': '14px',
                'md': '16px',
                'lg': '18px',
                'xl': '20px',
                'display-xs': '24px',
                'display-sm': '30px',
                'display-md': '36px',
                'display-lg': '48px',
                'display-xl': '60px',
                'display-2xl':'72px',
            },
            lineHeight: {
                'xxs': '16px',
                'xs': '18px',
                'sm': '20px',
                'md': '24px',
                'lg': '28px',
                'xl': '30px',
                'display-xs': '32px',
                'display-sm': '38px',
                'display-md': '44px',
                'display-lg': '60px',
                'display-xl': '72px',
                'display-2xl': '90px',
            },
            borderRadius: {
                'none':'0px',
                'xxs':'2px',
                'xs':'4px',
                'sm':'6px',
                'md':'8px',
                'lg':'10px',
                'xl':'12px',
                '2xl':'16px',
                '3xl':'20px',
                '4xl':'24px',
                'full':'9999px',
            },

        },
        backgroundColor: {
            'primary': {
                'default': '#299D55',
                'hover': '#198C43',
                'pressed': '#198C43',
                'focused': '#299D55',
                'disabled': '#F5F5F5'
            },
            'secondary-gray': {
                'default': '#FFFFFF',
                'hover': '#FAFAFA',
                'pressed': '#FAFAFA',
                'focused': '#FFFFFF',
                'disabled': '#FFFFFF'
            },
            'secondary-color': {
                'default': '#FFFFFF',
                'hover': '#F0FFF7',
                'pressed': '#F0FFF7',
                'focused': '#FFFFFF',
                'disabled': '#FFFFFF'
            },
            'utility-brand-50': '#F0FFF7'
        },
        textColor: {
            'primary': {
                'default': '#FFFFFF',
                'hover': '#FFFFFF',
                'pressed': '#FFFFFF',
                'focused': '#FFFFFF',
                'disabled': '#A4A7AE'
            },
            'secondary-gray': {
                'default': '#414651',
                'hover': '#252B37',
                'pressed': '#252B37',
                'focused': '#414651',
                'disabled': '#A4A7AE'
            },
            'secondary-color': {
                'default': '#198C43',
                'hover': '#146630',
                'pressed': '#146630',
                'focused': '#198C43',
                'disabled': '#A4A7AE'
            }
        },
        borderColor: {
            'primary': {
                'default': '#299D55',
                'hover': '#198C43',
                'pressed': '#198C43',
                'focused': '#299D55',
                'disabled': '#E9EAEB'
            },
            'secondary-gray': {
                'default': '#D5D7DA',
                'hover': '#D5D7DA',
                'pressed': '#D5D7DA',
                'focused': '#D5D7DA',
                'disabled': '#E9EAEB'
            },
            'secondary-color': {
                'default': '#8EE5BA',
                'hover': '#8EE5BA',
                'pressed': '#8EE5BA',
                'focused': '#8EE5BA',
                'disabled': '#E9EAEB'
            },
            'utility-brand-200':'#B5F2D7'
        },
        screens: {
            xs: '320px',
            sm: '640px',
            md: '960px',
            lg: '1024px',
            'custom': '1142px',
            'custom3': '1200px',
            'custom2': '1166px',
            'custom4': '1055px',
            xl: '1440px',


        },
        colors: {
            'Colors-Text-text-disabled': '#717680',
            'quaternary-500': '#181D27',
            'white': '#ffffff'
        },
        fontFamily: {
            'inter': ['Inter', 'sans-serif'], // Thêm font Inter
        },

    },
    plugins: [
        require('@tailwindcss/typography'),
        require('@tailwindcss/line-clamp'),
    ],
}

